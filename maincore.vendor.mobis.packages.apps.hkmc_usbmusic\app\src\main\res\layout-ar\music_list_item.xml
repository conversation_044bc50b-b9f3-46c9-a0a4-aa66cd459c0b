<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_list_item.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#       Date(yyyy-mm-dd)          By                  Description
 ******************************************************************************
     1.0              2017-02-03          Rezwana Begum    music list item xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:music="http://schemas.android.com/apk/res/com.daudio.av.app.usbmusic"
    android:layout_width="match_parent"
    android:layout_height="72dip" >

    <View
        android:id="@+id/list_divider"
        android:layout_width="match_parent"
        android:layout_height="1.3dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="0dip"
        android:layout_marginTop="70.7dip"
        android:background="@drawable/co_bg_list_line" />

    <com.daudio.av.app.usbmusic.ui.widget.list.MusicListItem
        android:id="@+id/list_item_text"
        android:layout_width="match_parent"
        android:layout_height="72dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="0dip"
        android:layout_marginTop="0dip"
        android:background="@drawable/music_list_item_text_bg"
        android:ellipsize="marquee"
        android:focusable="true"
        android:gravity="center_vertical|right"
        android:marqueeRepeatLimit="marquee_forever"
        android:paddingLeft="22dip"
        android:paddingRight="108dip"
        android:paddingTop="0dip"
        android:singleLine="true"
        android:textColor="@drawable/music_list_item_bg_color"
        android:textSize="26sp"
        music:checkImage="@drawable/music_list_checkbox"
        music:checkImageLeft="90dip"
        music:checkImageTop="16dip"
        music:checkImageVisible="false"
        music:focusImage="@drawable/co_btn_bg_list_focus"
        music:folderImage="@drawable/music_list_item_icon_folder"
        music:iconImageHeight="40dip"
        music:iconImageWidth="40dip"
        music:iconImageLeft="556dip"
        music:iconImageSecondLeft="548dip"
        music:iconImageTop="13dip"
        music:isTextScroll="false"
        music:indexerIndicatorImage="@drawable/co_bg_index_01"
        music:indexerIndicatorImageHeight="72dip"
        music:indexerIndicatorImageWidth="44dip"
        music:isCheckable="false"
        music:resizable="true"
        music:secondViewPaddingRight="94dip"
        music:secondViewPaddingLeft="132dip"
        music:secondViewTextSize="26px"
        music:songPauseImage="@drawable/music_list_item_icon_song_pause"
        music:songPlayImage="@drawable/music_list_item_icon_song_play"
        music:songPlayAnimationImage="@drawable/music_list_item_icon_song_play_animation"
        music:songPauseAnimationImage="@drawable/music_list_item_icon_song_pause_animation"
        music:folderPlayAnimationImage="@drawable/music_list_item_icon_folder_play_animation"
        music:folderPauseAnimationImage="@drawable/music_list_item_icon_folder_pause_animation"
        music:subItemIconPaddingLeft="-40dip"
        music:subItemPaddingRight="38dip"
        music:userListPauseImage="@drawable/music_list_item_icon_user_list_pause"
        music:userListPlayImage="@drawable/music_list_item_icon_user_list_play" />

    <com.daudio.av.app.usbmusic.ui.widget.list.MusicListCheckButton
        android:id="@+id/list_item_checkbutton"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="end|center_vertical"
        android:layout_marginLeft="90dip"
        android:button="@drawable/music_list_checkbox"/>

    <TextView
        android:id="@+id/list_item_only_text"
        android:layout_width="match_parent"
        android:layout_height="72dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="0dip"
        android:layout_marginTop="0dip"
        android:ellipsize="marquee"
        android:focusable="true"
        android:visibility="gone"
        android:gravity="center_vertical|right"
        android:marqueeRepeatLimit="marquee_forever"
        android:paddingLeft="22dip"
        android:paddingRight="58dip"
        android:paddingTop="0dip"
        android:singleLine="true"
        android:textColor="@drawable/music_list_item_bg_color"
        android:textSize="26sp" />

    <TextView
        android:id="@+id/list_item_indexer_indicator"
        android:layout_width="44dip"
        android:layout_height="72dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="614dip"
        android:layout_marginTop="0dip"
        android:gravity="center"
        android:background="@drawable/co_bg_index_01"
        android:paddingTop="6dip"
        android:textColor="@color/text_list_indexer_item"
        android:textSize="26sp" />

    <View
        android:id="@+id/list_item_co_bg_index_line"
        android:layout_width="44dip"
        android:layout_height="2dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="614dip"
        android:layout_marginTop="70dip" />



</FrameLayout>