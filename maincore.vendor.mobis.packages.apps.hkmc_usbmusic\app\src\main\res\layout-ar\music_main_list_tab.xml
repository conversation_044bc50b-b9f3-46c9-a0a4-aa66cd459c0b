<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_main_list_tab.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#       Date(yyyy-mm-dd)             By                  Description
 ******************************************************************************
     1.0              2017-02-03            Samarth Dubey    main view list tab xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:music="http://schemas.android.com/apk/res/com.daudio.av.app.usbmusic"
    android:layout_width="match_parent"
    android:layout_height="480dip" >

    <FrameLayout
        android:id="@+id/aniBody"
        android:layout_width="426dip"
        android:layout_height="480dip">

        <com.daudio.av.app.usbmusic.ui.widget.list.MusicListTitle
            android:id="@+id/music_tab_list_parent_btn"
            android:layout_width="match_parent"
            android:layout_height="72dip"
            android:layout_gravity="left|top"
            android:layout_marginLeft="0dip"
            android:layout_marginTop="120dip"
            android:background="@drawable/music_list_bg"
            android:ellipsize="end"
            android:gravity="center_vertical|right"
            android:paddingRight="70dip"
            android:singleLine="true"
            android:textColor="@color/text_normal"
            android:textSize="26sp"
            music:songDefaultText="@string/list_tap_song"
            music:albumDefaultText="@string/list_tap_album"
            music:artistDefaultText="@string/list_tap_artist"
            music:fileDefaultText="@string/list_top_folder"
            music:recentlyAddedDefaultText="@string/list_tap_recently_added"
            music:firstExtraImage="@drawable/music_list_parent_icon"
            music:firstExtraImageHeight="40dip"
            music:firstExtraImageWidth="40dip"
            music:firstExtraImageLeft="364dip"
            music:firstExtraImageTop="16dip" />

        <com.daudio.av.app.usbmusic.ui.widget.list.MediaCustomListView
            android:id="@+id/music_main_tab_list"
            android:layout_width="424dip"
            android:layout_height="288dip"
            android:layout_gravity="left|top"
            android:layout_marginLeft="0dip"
            android:layout_marginTop="192dip"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbars="vertical"
            android:dividerHeight="0dp"
            android:divider="@null"
            android:verticalScrollbarPosition="left"/>

    </FrameLayout>

     <View
        android:layout_width="2dip"
        android:layout_height="360dip"
        android:layout_marginLeft="424dip"
        android:layout_marginTop="120dip"
        android:background="@drawable/co_bg_list_tabline" />

</FrameLayout>
