<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android" >
    <item
        android:id="@+id/menu_display_on_off"
        android:title="@string/display_on_off"/>
        
    <item
        android:id="@+id/music_menu_media_sources"
        android:title="@string/music_menu_media_sources"/>
        
    <item
        android:id="@+id/music_menu_current_artistCategory"
        android:title="@string/music_menu_current_artistCategory"/>
        
    <item
        android:id="@+id/music_menu_current_albumCategory"
        android:title="@string/music_menu_current_albumCategory"/>
    <item
        android:id="@+id/music_menu_hide_currentfile"
        android:title="@string/music_menu_hide_currentfile"/>
        
    <item
        android:id="@+id/music_menu_removeall_favorite"
        android:title="@string/menu_removeall_favorite"/>

    <item
        android:id="@+id/music_menu_removeall_favorite_1"
        android:title="@string/menu_removeall_favorite_1"/>
        
    <item
        android:id="@+id/list_menu_now_playing"
        android:title="@string/list_menu_now_playing"/>
        
    <item
        android:id="@+id/music_menu_sound_settings"
        android:title="@string/music_menu_sound_settings"/>    
        
    <item
        android:id="@+id/web_manual"
        android:title="@string/music_dropdown_web_manual"/>

    <!--Modified by thunderSoft start : ccnc delete split screen in menu list-->
    <!--<item
        android:id="@+id/menu_split_screen"
        android:checkable="true"
        android:checked="false"
        android:showAsAction="always"
        android:title="@string/split_view"
        android:visible="true"/>   -->
    <!--Modified by thunderSoft end-->
</menu>
