<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="MusicListItem">
        <attr name="folderImage" format="integer" />
        <attr name="songPlayImage" format="integer" />
        <attr name="songPlayAnimationImage" format="integer" />
        <attr name="folderPlayAnimationImage" format="integer" />
        <attr name="folderPauseAnimationImage" format="integer" />
        <attr name="songPauseImage" format="integer" />
        <attr name="userListPlayImage" format="integer" />
        <attr name="userListPauseImage" format="integer" />
        <attr name="artistImage" format="integer" />
        <attr name="albumImage" format="integer" />
        <attr name="folderImageVisible" format="boolean" />
        <attr name="folderPlayImageAnimationVisible" format="boolean" />
        <attr name="folderPauseImageAnimationVisible" format="boolean" />
        <attr name="songPlayImageVisible" format="boolean" />
        <attr name="songPlayImageAnimationVisible" format="boolean" />
        <attr name="songPauseImageVisible" format="boolean" />
        <attr name="userListPlayImageVisible" format="boolean" />
        <attr name="userListPauseImageVisible" format="boolean" />
        <attr name="artistImageVisible" format="boolean" />
        <attr name="albumImageVisible" format="boolean" />
        <attr name="songPauseAnimationImage" format="integer" />
        <attr name="songPauseImageAnimationVisible" format="boolean" />
	</declare-styleable>
</resources>
