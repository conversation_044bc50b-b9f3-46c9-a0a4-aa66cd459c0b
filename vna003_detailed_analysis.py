#!/usr/bin/env python3
"""
Detailed analysis of VNA-003 issues from secure coding CSV
"""

import pandas as pd

def analyze_vna003_issues():
    """Analyze VNA-003 issues in detail"""
    try:
        # Read CSV file
        df = pd.read_csv('secure coding all.csv')
        
        # Filter VNA-003 issues
        vna003 = df[df['Checker'] == 'HYUNDAI MJ-VNA-003']
        
        print("=" * 80)
        print("VNA-003 DETAILED ANALYSIS")
        print("=" * 80)
        print(f"Total VNA-003 issues: {len(vna003)}")
        print(f"Percentage of all issues: {(len(vna003)/len(df)*100):.1f}%")
        print()
        
        # Status breakdown
        print("📊 VNA-003 STATUS BREAKDOWN")
        print("-" * 40)
        status_counts = vna003['Status'].value_counts()
        for status, count in status_counts.items():
            percentage = (count / len(vna003)) * 100
            print(f"{status}: {count} ({percentage:.1f}%)")
        print()
        
        # Top files with VNA-003 issues
        print("📁 TOP 20 FILES WITH VNA-003 ISSUES")
        print("-" * 40)
        file_counts = vna003['File'].value_counts().head(20)
        for i, (file_path, count) in enumerate(file_counts.items(), 1):
            filename = file_path.split('/')[-1] if '/' in file_path else file_path
            print(f"{i:2d}. {filename}: {count} issues")
        print()
        
        # Status breakdown by top files
        print("🎯 VNA-003 STATUS BY TOP 10 FILES")
        print("-" * 40)
        for file_path, total_count in file_counts.head(10).items():
            filename = file_path.split('/')[-1] if '/' in file_path else file_path
            file_issues = vna003[vna003['File'] == file_path]
            status_counts = file_issues['Status'].value_counts()
            new_count = status_counts.get('New', 0)
            fixed_count = status_counts.get('Fixed', 0)
            fix_rate = (fixed_count / total_count * 100) if total_count > 0 else 0
            print(f"{filename}:")
            print(f"  Total: {total_count}, New: {new_count}, Fixed: {fixed_count}, Fix Rate: {fix_rate:.1f}%")
        print()
        
        # Function analysis
        print("🔧 TOP FUNCTIONS WITH VNA-003 ISSUES")
        print("-" * 40)
        function_counts = vna003['Function'].value_counts().head(15)
        for func, count in function_counts.items():
            # Truncate long function names
            func_display = func[:60] + "..." if len(func) > 60 else func
            print(f"{func_display}: {count}")
        print()
        
        # Framework services analysis
        print("🏗️ FRAMEWORK SERVICES VNA-003 ANALYSIS")
        print("-" * 40)
        framework_issues = vna003[vna003['File'].str.contains('framework/services', na=False)]
        print(f"Framework services VNA-003 issues: {len(framework_issues)}")
        if len(framework_issues) > 0:
            framework_files = framework_issues['File'].value_counts()
            print("Top framework service files:")
            for file_path, count in framework_files.items():
                filename = file_path.split('/')[-1] if '/' in file_path else file_path
                print(f"  {filename}: {count}")
        print()
        
        # Package analysis
        print("📦 VNA-003 ISSUES BY PACKAGE TYPE")
        print("-" * 40)
        package_analysis = {}
        for file_path in vna003['File'].unique():
            if 'framework/services' in file_path:
                package_type = 'Framework Services'
            elif 'packages/apps' in file_path:
                package_type = 'Applications'
            elif 'packages/services' in file_path:
                package_type = 'System Services'
            else:
                package_type = 'Other'
            
            if package_type not in package_analysis:
                package_analysis[package_type] = 0
            package_analysis[package_type] += len(vna003[vna003['File'] == file_path])
        
        for package_type, count in sorted(package_analysis.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / len(vna003)) * 100
            print(f"{package_type}: {count} ({percentage:.1f}%)")
        print()
        
        # Priority recommendations
        print("🎯 PRIORITY RECOMMENDATIONS")
        print("-" * 40)
        print("1. HIGH PRIORITY FILES (>20 VNA-003 issues):")
        high_priority = file_counts[file_counts > 20]
        for file_path, count in high_priority.items():
            filename = file_path.split('/')[-1] if '/' in file_path else file_path
            file_issues = vna003[vna003['File'] == file_path]
            new_count = len(file_issues[file_issues['Status'] == 'New'])
            print(f"   {filename}: {new_count} unfixed issues")
        
        print("\n2. MEDIUM PRIORITY FILES (10-20 VNA-003 issues):")
        medium_priority = file_counts[(file_counts >= 10) & (file_counts <= 20)]
        for file_path, count in medium_priority.items():
            filename = file_path.split('/')[-1] if '/' in file_path else file_path
            file_issues = vna003[vna003['File'] == file_path]
            new_count = len(file_issues[file_issues['Status'] == 'New'])
            print(f"   {filename}: {new_count} unfixed issues")
        
        print(f"\n3. TOTAL UNFIXED VNA-003 ISSUES: {len(vna003[vna003['Status'] == 'New'])}")
        print(f"4. ESTIMATED EFFORT: {len(vna003[vna003['Status'] == 'New']) * 0.5:.0f} developer-hours")
        print("   (Assuming 30 minutes per VNA-003 fix)")
        
        return vna003
        
    except Exception as e:
        print(f"Error analyzing VNA-003 issues: {e}")
        return None

if __name__ == "__main__":
    vna003_data = analyze_vna003_issues()
