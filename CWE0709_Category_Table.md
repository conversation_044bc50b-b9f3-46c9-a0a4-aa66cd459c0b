# CWE-0709 Category Breakdown Table

**Date**: 2025-07-09  
**Source**: CWE0709.csv  
**Total Issues**: 1,218  

---

## 📊 Complete Category Breakdown

| Rank | Category | Count | Percentage | Priority | Status (New/Dismissed) |
|------|----------|-------|------------|----------|------------------------|
| 1 | **Low impact security** | 366 | 30.0% | **MEDIUM** | 291/75 |
| 2 | **Concurrent data access violations** | 305 | 25.0% | **HIGH** | 250/55 |
| 3 | **Null pointer dereferences** | 125 | 10.3% | **MEDIUM** | 49/72 |
| 4 | **Sigma** | 98 | 8.0% | **LOW** | 92/6 |
| 5 | **Control flow issues** | 96 | 7.9% | **LOW** | 80/16 |
| 6 | **Medium impact security** | 74 | 6.1% | **HIGH** | 59/15 |
| 7 | **Integer handling issues** | 39 | 3.2% | **LOW** | 32/7 |
| 8 | **Exceptional resource leaks** | 21 | 1.7% | **MEDIUM** | 16/5 |
| 9 | **Resource leaks** | 20 | 1.6% | **MEDIUM** | 16/4 |
| 10 | **High impact security** | 19 | 1.6% | **CRITICAL** | 15/4 |
| 11 | **Error handling issues** | 16 | 1.3% | **LOW** | 13/3 |
| 12 | **Data race undermines locking** | 16 | 1.3% | **HIGH** | 13/3 |
| 13 | **Incorrect expression** | 10 | 0.8% | **LOW** | 8/2 |
| 14 | **Program hangs** | 9 | 0.7% | **HIGH** | 7/2 |
| 15 | **Class hierarchy inconsistencies** | 2 | 0.2% | **LOW** | 1/1 |
| 16 | **Possible Control flow issues** | 1 | 0.1% | **LOW** | 1/0 |
| 17 | **Code maintainability issues** | 1 | 0.1% | **LOW** | 1/0 |
| **TOTAL** | | **1,218** | **100.0%** | | **883/330** |

---

## 🔒 Security Risk Level Analysis

| Risk Level | Categories | Total Issues | Percentage | Unresolved |
|------------|------------|--------------|------------|------------|
| **CRITICAL** | High impact security | **19** | **1.6%** | 15 |
| **HIGH** | Concurrent data access violations, Medium impact security, Data race undermines locking, Program hangs | **404** | **33.2%** | 329 |
| **MEDIUM** | Low impact security, Null pointer dereferences, Exceptional resource leaks, Resource leaks | **532** | **43.7%** | 372 |
| **LOW** | Sigma, Control flow issues, Integer handling issues, Error handling issues, Incorrect expression, Class hierarchy inconsistencies, Possible Control flow issues, Code maintainability issues | **263** | **21.6%** | 167 |

---

## 🎯 Top 5 Categories Detailed Analysis

### 1. Low Impact Security (366 issues - 30.0%)
- **Status**: 291 New, 75 Dismissed
- **Top Issue Types**:
  - Missing permission for broadcast: 325 instances
  - File on external storage: 19 instances
  - Information exposure to log file: 19 instances
- **Risk**: Information disclosure, privilege escalation

### 2. Concurrent Data Access Violations (305 issues - 25.0%)
- **Status**: 250 New, 55 Dismissed
- **Top Issue Types**:
  - Unguarded read: 150 instances
  - Unlocked write: 104 instances
  - Unguarded write: 32 instances
- **Risk**: Data corruption, race conditions, system instability

### 3. Null Pointer Dereferences (125 issues - 10.3%)
- **Status**: 49 New, 72 Dismissed
- **Top Issue Types**:
  - Dereference null return value: 43 instances
  - Dereference after null check: 42 instances
  - Explicit null dereferenced: 21 instances
- **Risk**: Application crashes, denial of service

### 4. Sigma (98 issues - 8.0%)
- **Status**: 92 New, 6 Dismissed
- **Top Issue Types**:
  - SQL Injection: 53 instances ⚠️ **CRITICAL**
  - Hard-coded secret: 15 instances
  - Insecure IPC in mobile applications: 10 instances
- **Risk**: Data breach, authentication bypass, privilege escalation

### 5. Control Flow Issues (96 issues - 7.9%)
- **Status**: 80 New, 16 Dismissed
- **Top Issue Types**:
  - Logically dead code: 69 instances
  - Missing break in switch: 25 instances
  - Stray semicolon: 2 instances
- **Risk**: Unexpected behavior, maintenance issues

---

## 🚨 Critical Priority Actions

### Immediate Action Required (CRITICAL - 19 issues)
- **High impact security**: 15 unresolved issues
- **Focus**: File system manipulation, sensitive data exposure
- **Timeline**: 1-2 weeks

### High Priority (HIGH - 404 issues)
- **Concurrent data access violations**: 250 unresolved
- **Medium impact security**: 59 unresolved
- **Data race undermines locking**: 13 unresolved
- **Program hangs**: 7 unresolved
- **Timeline**: 2-6 weeks

### Medium Priority (MEDIUM - 532 issues)
- **Low impact security**: 291 unresolved
- **Null pointer dereferences**: 49 unresolved
- **Resource leaks**: 32 unresolved
- **Timeline**: 1-3 months

---

## 📈 Resolution Progress by Category

| Category | Total | Resolved | Resolution Rate | Remaining Work |
|----------|-------|----------|-----------------|----------------|
| **Null pointer dereferences** | 125 | 76 | **60.8%** | 49 |
| **High impact security** | 19 | 4 | **21.1%** | 15 |
| **Concurrent data access violations** | 305 | 55 | **18.0%** | 250 |
| **Control flow issues** | 96 | 16 | **16.7%** | 80 |
| **Low impact security** | 366 | 75 | **20.5%** | 291 |
| **Sigma** | 98 | 6 | **6.1%** | 92 |

**Best Performance**: Null pointer dereferences (60.8% resolved)  
**Needs Attention**: Sigma category (6.1% resolved) - contains critical SQL injection issues

---

## 📊 Summary Statistics

- **Total Categories**: 17
- **Security-Related Categories**: 4 (High, Medium, Low impact security + Sigma)
- **Thread Safety Categories**: 3 (Concurrent data access, Data race, Program hangs)
- **Code Quality Categories**: 10 (remaining categories)

**Key Insight**: 55.5% of all issues are security-related (676 out of 1,218), requiring immediate security-focused remediation strategy.

---

**Generated**: 2025-07-09  
**Next Update**: Weekly category progress review recommended
