<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_list.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#    Date(yyyy-mm-dd)        By                  Description
 ******************************************************************************
     1.0            2017-02-03         Rezwana Begum    list view  xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:music="http://schemas.android.com/apk/res/com.daudio.av.app.usbmusic"
    android:layout_width="match_parent"
    android:layout_height="480dip">

    <ImageView
        android:id="@+id/music_list_bg"
        android:layout_width="match_parent"
        android:layout_height="420dip"
        android:layout_marginTop="60dip"
        android:background="@android:drawable/co_bg"/>

    <com.daudio.av.app.usbmusic.ui.widget.group.MediaTabGroup
        android:id="@+id/list_category"
        android:layout_width="196dip"
        android:layout_height="360dip"
        android:layout_marginTop="120dip"
        music:selectedButton="@+id/list_file_btn" >

        <com.daudio.av.app.usbmusic.ui.widget.button.MediaTabButton
            android:id="@+id/list_file_btn"
            android:layout_width="196dip"
            android:layout_height="72dip"
            android:layout_gravity="right|top"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="0dip"
            android:background="@drawable/music_list_tab_button_bg"
            android:gravity="right|center_vertical"
            android:paddingRight="24dip"
            android:paddingTop="5dip"
            android:text="@string/list_tap_file"
            android:textColor="@drawable/music_list_tab_color"
            android:textSize="26sp" />

        <View
            android:layout_width="196dip"
            android:layout_height="1.3dip"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="70.7dip"
            android:background="@drawable/co_bg_list_line" />


       <com.daudio.av.app.usbmusic.ui.widget.button.MediaTabButton
            android:id="@+id/list_artist_btn"
            android:layout_width="196dip"
            android:layout_height="72dip"
            android:layout_gravity="right|top"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="72dip"
            android:background="@drawable/music_list_tab_button_bg"
            android:gravity="right|center_vertical"
            android:paddingRight="24dip"
            android:paddingTop="5dip"
            android:text="@string/list_tap_artist"
            android:textColor="@drawable/music_list_tab_color"
            android:textSize="26sp" />

        <View
            android:layout_width="196dip"
            android:layout_height="1.3dip"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="142.7dip"
            android:background="@drawable/co_bg_list_line" />

        <com.daudio.av.app.usbmusic.ui.widget.button.MediaTabButton
            android:id="@+id/list_song_btn"
            android:layout_width="196dip"
            android:layout_height="72dip"
            android:layout_gravity="right|top"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="144dip"
            android:background="@drawable/music_list_tab_button_bg"
            android:gravity="right|center_vertical"
            android:paddingRight="24dip"
            android:paddingTop="5dip"
            android:text="@string/list_tap_song"
            android:textColor="@drawable/music_list_tab_color"
            android:textSize="26sp" />

        <View
            android:layout_width="196dip"
            android:layout_height="1.3dip"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="214.7dip"
            android:background="@drawable/co_bg_list_line" />

        <com.daudio.av.app.usbmusic.ui.widget.button.MediaTabButton
            android:id="@+id/list_album_btn"
            android:layout_width="196dip"
            android:layout_height="72dip"
            android:layout_gravity="right|top"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="216dip"
            android:background="@drawable/music_list_tab_button_bg"
            android:gravity="right|center_vertical"
            android:paddingRight="24dip"
            android:paddingTop="5dip"
            android:text="@string/list_tap_album"
            android:textColor="@drawable/music_list_tab_color"
            android:textSize="26sp" />

        <View
            android:layout_width="196dip"
            android:layout_height="1.3dip"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="286.7dip"
            android:background="@drawable/co_bg_list_line" />

        <com.daudio.av.app.usbmusic.ui.widget.button.MediaTabButton
            android:id="@+id/list_recently_added_btn"
            android:layout_width="196dip"
            android:layout_height="72dip"
            android:layout_gravity="right|top"
            android:layout_marginLeft="658dip"
            android:layout_marginTop="288dip"
            android:background="@drawable/music_list_tab_button_bg"
            android:paddingBottom="7dip"
            android:paddingRight="24dip"
            android:paddingLeft="24dip"
            android:gravity="right|center_vertical"
            android:text="@string/list_tap_recently_added"
            android:textColor="@drawable/music_list_tab_color"
            android:textSize="26sp"
            android:visibility="gone" />

        <com.daudio.av.app.usbmusic.ui.widget.button.MediaTabButton
            android:id="@+id/list_favorites_btn"
            android:layout_width="196dip"
            android:layout_height="72dip"
            android:layout_gravity="left|top"
            android:layout_marginLeft="0dip"
            android:layout_marginTop="288dip"
            android:background="@drawable/music_list_tab_button_bg"
            android:paddingBottom="7dip"
            android:paddingLeft="24dip"
            android:paddingRight="24dip"
            android:gravity="left|center_vertical"
            android:text="@string/list_tap_favorite"
            android:textColor="@drawable/music_list_tab_color"
            android:textSize="26sp"
            android:visibility="gone" />

        <com.daudio.av.app.usbmusic.ui.widget.button.MediaTabButton
            android:id="@+id/list_favorites_btn_1"
            android:layout_width="196dip"
            android:layout_height="72dip"
            android:layout_gravity="left|top"
            android:layout_marginLeft="0dip"
            android:layout_marginTop="288dip"
            android:background="@drawable/music_list_tab_button_bg"
            android:paddingBottom="7dip"
            android:paddingLeft="24dip"
            android:paddingRight="24dip"
            android:gravity="left|center_vertical"
            android:text="@string/list_tap_favorite_1"
            android:textColor="@drawable/music_list_tab_color"
            android:textSize="26sp"
            android:visibility="gone" />

        <View
            android:layout_width="196dip"
            android:layout_height="1.3dip"
            android:layout_marginLeft="0dip"
            android:layout_marginTop="358.7dip"
            android:background="@drawable/co_bg_list_line" />

        <View
            android:layout_width="2dip"
            android:layout_height="360dip"
            android:layout_marginRight="194dip"
            android:layout_marginTop="0dip"
            android:background="@drawable/co_bg_list_tabline" />

    </com.daudio.av.app.usbmusic.ui.widget.group.MediaTabGroup>

    <LinearLayout
        android:id="@+id/aniBody"
        android:layout_width="854dip"
        android:layout_height="360dip"
        android:layout_marginLeft="0dip"
        android:orientation="vertical"
        android:layout_marginTop="120dip" >

        <com.daudio.av.app.usbmusic.ui.widget.list.MusicListTitle
            android:id="@+id/list_parent_btn"
            android:layout_width="658dip"
            android:layout_height="72dip"
            android:layout_gravity="left|top"
            android:layout_marginRight="196dip"
            android:layout_marginTop="0dip"
            android:background="@drawable/music_list_bg"
            android:ellipsize="end"
            android:gravity="center_vertical|right"
            android:paddingRight="70dip"
            android:paddingLeft="120dip"
            android:singleLine="true"
            android:textColor="@color/text_normal"
            android:textSize="26sp"
            music:albumDefaultText="@string/list_tap_album"
            music:artistDefaultText="@string/list_tap_artist"
            music:fileDefaultText="@string/list_top_folder"
            music:firstExtraImage="@drawable/music_list_parent_icon"
            music:firstExtraImageHeight="40dip"
            music:firstExtraImageWidth="40dip"
            music:firstExtraImageLeft="596dip"
            music:firstExtraImageTop="16dip"
            music:recentlyAddedDefaultText="@string/list_tap_recently_added"
            music:resizable="true"
            music:secondViewLeft="0dip"
            music:secondViewPaddingRight="70dip"
            music:secondViewWidth="854dip"
            music:songDefaultText="@string/list_tap_song"
            music:userListDefaultText="@string/list_tap_user_list" />

        <com.daudio.av.app.usbmusic.ui.widget.list.MediaCustomListView
            android:id="@+id/music_list"
            android:layout_width="658dip"
            android:layout_height="match_parent"
            android:layout_gravity="left|top"
            android:layout_marginRight="196dip"
            android:layout_marginTop="0dip"
            android:scrollbars="vertical"
            android:scrollbarStyle="outsideOverlay"
            music:resizable="true"
            music:secondViewHeight="288dip"
            music:secondViewLeft="0dip"
            music:secondViewWidth="658dip" />

        <TextView
            android:id="@+id/music_list_empty_cover"
            android:layout_width="658dip"
            android:layout_height="match_parent"
            android:layout_gravity="left|top"
            android:layout_marginRight="196dip"
            android:layout_marginTop="0dip"
            android:gravity="center"
            android:text="@string/no_supported_file"
            android:textColor="@color/text_list_item_nor"
            android:textSize="27sp"
            android:visibility="gone" />

    </LinearLayout>

    <TextView
        android:id="@+id/music_list_empty_favourite_cover"
        android:layout_width="658dip"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="196dip"
        android:layout_marginTop="60dip"
        android:gravity="center"
        android:text="@string/list_no_favorite"
        android:textColor="@color/text_list_item_nor"
        android:textSize="27sp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/music_list_empty_favourite_cover_1"
        android:layout_width="658dip"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="196dip"
        android:layout_marginTop="60dip"
        android:gravity="center"
        android:text="@string/list_no_favorite_1"
        android:textColor="@color/text_list_item_nor"
        android:textSize="27sp"
        android:visibility="gone" />

    <FrameLayout
        android:layout_width="854dip"
        android:layout_height="72dip"
        android:layout_marginStart="0dip"
        android:layout_marginTop="120dip" >
        <Button
            android:id="@+id/play_all_btn"
            android:onClick="onPlayAllBtn"
            android:layout_width="92dip"
            android:layout_height="52dip"
            android:layout_marginEnd="6dip"
            android:layout_marginTop="10dip"
            android:paddingRight="10dip"
            android:paddingLeft="10dip"
            android:layout_gravity="end|top"
            android:singleLine="true"
            android:text="@string/list_play_button"
            android:gravity="center"
            android:textColor="@color/text_music_list"
            android:textSize="26sp"
            android:background="@drawable/music_main_button_bg"
            android:visibility="gone"/>
    </FrameLayout>

    <com.daudio.av.app.usbmusic.ui.widget.list.MediaListIndexer
        android:id="@+id/music_list_indexer"
        android:layout_width="854dip"
        android:layout_height="match_parent"
        android:visibility="gone"
        music:arabicIndexerViewBgImage="@drawable/co_bg_index_a_02"
        music:englishIndexerViewBgImage="@drawable/co_bg_index_e_02"
        music:indexerIndicatorId="@+id/music_list_indexer_indicator"
        music:indexerViewId="@+id/music_list_indexer_view"
        music:koreanIndexerViewBgImage="@drawable/co_bg_index_k_02"
        music:japanIndexerViewBgImage="@drawable/co_bg_index_j_02"
        music:validMinCount="5" >

        <TextView
            android:id="@+id/music_list_indexer_indicator"
            android:layout_width="176dip"
            android:layout_height="176dip"
            android:layout_gravity="left|top"
            android:layout_marginLeft="218dip"
            android:layout_marginTop="248dip"
            android:background="@drawable/co_bg_index_pop"
            android:gravity="center"
            android:textColor="@color/text_normal"
            android:textSize="76sp"
            android:textStyle="bold" />

        <View
            android:id="@+id/music_list_indexer_view"
            android:layout_width="72dip"
            android:layout_height="288dip"
            android:layout_gravity="left|top"
            android:layout_marginLeft="10dip"
            android:layout_marginTop="192dip"
            android:background="@drawable/co_bg_index_e_01" />
    </com.daudio.av.app.usbmusic.ui.widget.list.MediaListIndexer>

    <include
        android:id="@+id/music_list_control"
        android:layout_width="658dp"
        android:layout_height="72dip"
        android:layout_marginTop="408dip"
        android:gravity="center_vertical"
        android:layout_marginStart="196dip"
        layout="@layout/music_list_control"
        android:visibility="gone" />

</FrameLayout>
