<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_list_splitview.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#     Date(yyyy-mm-dd)          By                  Description
 ******************************************************************************
     1.0           2017-02-03           Rezwana Begum    split view song info xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="426dip"
    android:layout_height="480dip" >

    <!-- Modified by thunderSoft start: remove me_right_bg -->
    <View
        android:id="@+id/music_split_bg"
        android:layout_width="426dp"
        android:layout_height="420dp"
        android:layout_marginTop="60dip" />


    <View
        android:id="@+id/music_list_split_area"
        android:layout_width="424dp"
        android:layout_height="360dp"
        android:layout_marginTop="120dip"
        android:background="@drawable/me_right_bg" />
    <!-- Modified by thunderSoft end -->

    <View
        android:id="@+id/co_bg_list_rightline"
        android:layout_width="2dip"
        android:layout_height="360dip"
        android:layout_marginLeft="0dip"
        android:layout_marginTop="120dip"
        android:background="@drawable/co_bg_list_tabline" />

    <FrameLayout
        android:layout_width="156dip"
        android:layout_height="156dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="131dip"
        android:layout_marginTop="144dip">
        <ImageView
            android:id="@+id/music_split_fit_album_art"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:adjustViewBounds="true" />
    </FrameLayout>    

    <ImageView
        android:id="@+id/music_split_album_art"
        android:layout_width="156dip"
        android:layout_height="156dip"
        android:layout_gravity="left"
        android:layout_marginLeft="131dip"
        android:layout_marginTop="144dip"
        android:adjustViewBounds="true" />

	<View
        android:id="@+id/music_split_album_art_border"
        android:layout_width="156dip"
        android:layout_height="156dip"
        android:layout_gravity="left"
		android:background="@drawable/me_bg_album_s"
        android:layout_marginLeft="131dip"
        android:layout_marginTop="144dip"
	    android:visibility="visible" />	

    <com.daudio.av.app.usbmusic.ui.view.main.MusicCircleView
        android:id="@+id/music_split_album_circle"
        android:layout_width="103dip"
        android:layout_height="103dip"
        android:layout_gravity="left"
        android:layout_marginLeft="164dip"
        android:layout_marginTop="174dip"
		android:visibility="gone"/>
		
   <View
        android:id="@+id/music_split_album_bg"
        android:layout_width="164dip"
        android:layout_height="156dip"
        android:layout_gravity="left"
        android:layout_marginLeft="131dip"
        android:layout_marginTop="144dip"
        android:visibility="gone"/>
		
    <TextView
        android:id="@+id/music_split_artist"
        android:layout_width="390dip"
        android:layout_height="32dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="18dip"
        android:layout_marginTop="360dip"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:text="@string/list_tap_artist"
        android:textColor="@color/text_normal"
        android:textSize="24sp" />

    <TextView
        android:id="@+id/music_split_title"
        android:layout_width="390dip"
        android:layout_height="32dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="18dip"
        android:layout_marginTop="328dip"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:text="@string/list_tap_song"
        android:textColor="@color/text_normal"
        android:textSize="24sp" />

    <TextView
        android:id="@+id/music_split_album"
        android:layout_width="390dip"
        android:layout_height="32dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="18dip"
        android:layout_marginTop="392dip"
        android:ellipsize="end"
        android:gravity="center"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:text="@string/list_tap_album"
        android:textColor="@color/text_album"
        android:textSize="24sp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="32dip"
        android:layout_gravity="center_horizontal|bottom">

       <TextView
            android:id="@+id/splitview_totaltime"
            android:layout_width="wrap_content"
           android:minWidth="92dp"
            android:layout_height="22dip"
            android:gravity="left|top"
            android:layout_marginLeft="328dp"
            android:layout_marginRight="4dp"   
            android:layout_marginBottom="6dp"
            android:singleLine="true"
            android:text="@string/total_time"
            android:textColor="@color/text_totaltime"
            android:textSize="18sp" />
            
        <ProgressBar
            android:id="@+id/splitview_progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="216dip"
            android:layout_height="16dip"
            android:layout_gravity="center"
            android:layout_marginBottom="6dp"
            android:max="1000"
            android:progress="0"
            android:progressDrawable="@drawable/music_progress_horizontal"
            android:thumb="@null"/>

        <TextView
            android:id="@+id/splitview_currenttime"
            android:layout_width="92dip"
            android:layout_height="22dip"
            android:gravity="right|top"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="6dp"
            android:singleLine="true"
            android:text="@string/current_time"
            android:textColor="@color/text_current_time"
            android:textSize="18sp" />
    </FrameLayout>
</FrameLayout>
