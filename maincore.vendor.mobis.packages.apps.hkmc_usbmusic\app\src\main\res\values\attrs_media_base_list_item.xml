<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="MediaBaseListItem">
        <attr name="focusImage" format="integer" />
        <attr name="indexerIndicatorImage" format="integer" />
        <attr name="checkImage" format="integer" />
        <attr name="iconImage" format="integer" />
        <attr name="backgroundDecoImage" format="integer" />
        <attr name="focusImageVisible" format="boolean" />
        <attr name="focusImageLeft" format="dimension" />
        <attr name="focusImageTop" format="dimension" />
        <attr name="focusImageWidth" format="dimension" />
        <attr name="focusImageHeight" format="dimension" />
        <attr name="focusImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>
        <attr name="backgroundDecoImageVisible" format="boolean" />
        <attr name="backgroundDecoImageLeft" format="dimension" />
        <attr name="backgroundDecoImageTop" format="dimension" />
        <attr name="backgroundDecoImageWidth" format="dimension" />
        <attr name="backgroundDecoImageHeight" format="dimension" />
        <attr name="backgroundDecoImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>
        <attr name="indexerIndicatorImageVisible" format="boolean" />
        <attr name="indexerIndicatorImageLeft" format="dimension" />
        <attr name="indexerIndicatorImageTop" format="dimension" />
        <attr name="indexerIndicatorImageWidth" format="dimension" />
        <attr name="indexerIndicatorImageHeight" format="dimension" />
        <attr name="indexerIndicatorImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>
        <attr name="checkImageVisible" format="boolean" />
        <attr name="checkImageLeft" format="dimension" />
        <attr name="checkImageTop" format="dimension" />
        <attr name="checkImageWidth" format="dimension" />
        <attr name="checkImageHeight" format="dimension" />
        <attr name="checkImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>
        <attr name="iconImageVisible" format="boolean" />
        <attr name="iconImageLeft" format="dimension" />
        <attr name="iconImageTop" format="dimension" />
        <attr name="iconImageWidth" format="dimension" />
        <attr name="iconImageHeight" format="dimension" />
        <attr name="iconImagePaddingRight" format="dimension" />
        <attr name="iconImageSecondLeft" format="dimension" />
        <attr name="iconImageSecondTop" format="dimension" />
        <attr name="iconImageSecondWidth" format="dimension" />
        <attr name="iconImageSecondHeight" format="dimension" />
        <attr name="iconImageSecondPaddingRight" format="dimension" />
        <attr name="iconImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>

        <attr name="subItemPaddingLeft" format="dimension" />
        <attr name="subItemPaddingRight" format="dimension" />
        <attr name="subItemIconPaddingLeft" format="dimension" />
        <attr name="subItemIconPaddingRight" format="dimension" />
    </declare-styleable>
</resources>
