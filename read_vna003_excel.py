#!/usr/bin/env python3
"""
Script to read VNA-003 errors from Excel file and identify specific methods and line numbers
"""

import pandas as pd
import sys
import os

def read_vna003_errors(excel_file):
    """Read VNA-003 errors from Excel file"""
    try:
        # Read Excel file
        df = pd.read_excel(excel_file)

        # Print column names to understand structure
        print("Column names:")
        for i, col in enumerate(df.columns):
            print(f"{i}: {col}")
        print()

        # Filter for VNA-003 errors specifically
        vna003_errors = df[df['Checker'] == 'HYUNDAI MJ-VNA-003']

        if vna003_errors.empty:
            print("No VNA-003 errors found in the Excel file")
            return

        print(f"Found {len(vna003_errors)} VNA-003 errors:")
        print("=" * 80)

        for idx, row in vna003_errors.iterrows():
            print(f"\nError #{idx + 1}:")
            print("-" * 40)
            print(f"CID: {row['CID']}")
            print(f"File: {row['File']}")
            print(f"Function: {row['Function']}")
            print(f"Line Number: {row['Line Number']}")
            print(f"Status: {row['Status']}")

        return vna003_errors

    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def main():
    excel_file = "frameworks (2).csv.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"Excel file {excel_file} not found")
        return
    
    print(f"Reading VNA-003 errors from {excel_file}")
    print("=" * 60)
    
    vna003_errors = read_vna003_errors(excel_file)
    
    if vna003_errors is not None and not vna003_errors.empty:
        print(f"\nSummary: Found {len(vna003_errors)} VNA-003 errors")
        
        # Try to extract file names and line numbers
        print("\nFile and line number analysis:")
        print("-" * 40)
        
        for idx, row in vna003_errors.iterrows():
            file_info = ""
            line_info = ""
            method_info = ""
            
            for col in vna003_errors.columns:
                value = str(row[col]).strip()
                if value and value != 'nan':
                    # Look for file names
                    if '.java' in value.lower() or '.cpp' in value.lower() or '.c' in value.lower():
                        file_info = value
                    # Look for line numbers
                    if value.isdigit() or 'line' in value.lower():
                        line_info = value
                    # Look for method names
                    if '(' in value and ')' in value:
                        method_info = value
            
            print(f"Error #{idx + 1}:")
            if file_info:
                print(f"  File: {file_info}")
            if line_info:
                print(f"  Line: {line_info}")
            if method_info:
                print(f"  Method: {method_info}")
            print()

if __name__ == "__main__":
    main()
