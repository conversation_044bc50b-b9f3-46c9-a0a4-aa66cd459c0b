<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_list_control.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#       Date(yyyy-mm-dd)         By                  Description
 ******************************************************************************
     1.0               2017-02-03         Rezwana Begum    list view controls xml file
 ******************************************************************************
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="658dp"
    android:layout_height="72dip"
    android:layout_marginTop="408dip"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <Button
        android:id="@+id/list_control_mark_all"
        android:layout_width="match_parent"
        android:layout_height="52dip"
        android:layout_weight="1"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/music_main_button_bg"
        android:onClick="onListControlClick"
        android:text="@string/list_mark_all"
        android:textColor="@drawable/music_control_list_button_color"
        android:textSize="26sp"
        android:visibility="visible"/>

    <Button
        android:id="@+id/list_control_unmark_all"
        android:layout_width="match_parent"
        android:layout_height="52dip"
        android:layout_weight="1"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/music_main_button_bg"
        android:onClick="onListControlClick"
        android:text="@string/list_unmark_all"
        android:textColor="@drawable/music_control_list_button_color"
        android:textSize="26sp"
        android:visibility="visible" />

    <Button
        android:id="@+id/list_control_play"
        android:layout_width="match_parent"
        android:layout_height="52dip"
        android:layout_weight="1"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/music_main_button_bg"
        android:onClick="onListControlClick"
        android:text="@string/list_play_button"
        android:textColor="@drawable/music_control_list_button_color"
        android:textSize="26sp" />

</LinearLayout>