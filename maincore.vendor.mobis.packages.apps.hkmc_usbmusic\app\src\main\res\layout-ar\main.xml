<?xml version="1.0" encoding="utf-8"?>

<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     main.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#      Date(yyyy-mm-dd)              By              Description
 ******************************************************************************
     1.0             2017-02-03              Samarth Dubey     main layout xml file 
 ******************************************************************************
     2.0             2017-10-03              Rezwana Begum     main layout xml file 
 ******************************************************************************
-->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:music="http://schemas.android.com/apk/res/com.daudio.av.app.usbmusic"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/usbmusic_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    
    <include 
        android:id="@+id/music_Loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/music_loading"
        android:visibility="gone" />

    <include
        android:id="@+id/music"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/music_main"
        android:visibility="gone" />

    <include
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/music_list"
        android:visibility="gone" />

    <include
        android:id="@+id/music_list_SplitView"
        android:layout_width="426dip"
        android:layout_height="480dip"
        android:layout_marginRight="854dip"
        layout="@layout/music_list_splitview"
        android:visibility="gone" />

    <include
        android:id="@+id/music_title_bar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/title_bar" />

<!--Modified by thunderSoft start : After deleted, the status bar becomes gray-->
<!--   <include-->
<!--        android:id="@+id/music_popup"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="420dip"-->
<!--        android:layout_marginTop="60dip"-->
<!--        layout="@layout/popup"-->
<!--        android:visibility="gone" />-->
<!--Modified by thunderSoft end-->

</FrameLayout>
