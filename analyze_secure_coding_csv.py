#!/usr/bin/env python3
"""
<PERSON>ript to analyze secure coding all.csv file and provide comprehensive statistics
"""

import pandas as pd
import sys
import os
from collections import Counter

def analyze_secure_coding_csv(csv_file):
    """Analyze the secure coding CSV file"""
    try:
        # Read CSV file
        df = pd.read_csv(csv_file)
        
        print("=" * 80)
        print("SECURE CODING ANALYSIS REPORT")
        print("=" * 80)
        print(f"File: {csv_file}")
        print(f"Total Issues: {len(df)}")
        print()
        
        # Basic statistics
        print("📊 BASIC STATISTICS")
        print("-" * 40)
        print(f"Total CIDs: {len(df)}")
        print(f"Columns: {list(df.columns)}")
        print()
        
        # Status analysis
        print("📈 STATUS BREAKDOWN")
        print("-" * 40)
        status_counts = df['Status'].value_counts()
        for status, count in status_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{status}: {count} ({percentage:.1f}%)")
        print()
        
        # Checker analysis
        print("🔍 CHECKER BREAKDOWN (Top 15)")
        print("-" * 40)
        checker_counts = df['Checker'].value_counts().head(15)
        for checker, count in checker_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{checker}: {count} ({percentage:.1f}%)")
        print()
        
        # Category analysis
        print("📂 CATEGORY BREAKDOWN")
        print("-" * 40)
        category_counts = df['Category'].value_counts()
        for category, count in category_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{category}: {count} ({percentage:.1f}%)")
        print()
        
        # Type analysis
        print("🏷️ TYPE BREAKDOWN (Top 10)")
        print("-" * 40)
        type_counts = df['Type'].value_counts().head(10)
        for type_name, count in type_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{type_name}: {count} ({percentage:.1f}%)")
        print()
        
        # File analysis
        print("📁 FILES WITH MOST ISSUES (Top 15)")
        print("-" * 40)
        file_counts = df['File'].value_counts().head(15)
        for file_path, count in file_counts.items():
            # Extract just the filename for readability
            filename = file_path.split('/')[-1] if '/' in file_path else file_path
            print(f"{filename}: {count} issues")
        print()
        
        # VNA-003 specific analysis
        print("🎯 VNA-003 SPECIFIC ANALYSIS")
        print("-" * 40)
        vna003_issues = df[df['Checker'] == 'HYUNDAI MJ-VNA-003']
        print(f"Total VNA-003 issues: {len(vna003_issues)}")
        if len(vna003_issues) > 0:
            print("VNA-003 Status breakdown:")
            vna003_status = vna003_issues['Status'].value_counts()
            for status, count in vna003_status.items():
                print(f"  {status}: {count}")
            
            print("\nVNA-003 Files (Top 10):")
            vna003_files = vna003_issues['File'].value_counts().head(10)
            for file_path, count in vna003_files.items():
                filename = file_path.split('/')[-1] if '/' in file_path else file_path
                print(f"  {filename}: {count}")
        print()
        
        # Security issues analysis
        print("🔒 SECURITY ISSUES ANALYSIS")
        print("-" * 40)
        security_issues = df[df['Category'] == 'Audit impact security']
        print(f"Total security issues: {len(security_issues)}")
        if len(security_issues) > 0:
            print("Security issue types:")
            security_types = security_issues['Checker'].value_counts()
            for checker, count in security_types.items():
                print(f"  {checker}: {count}")
            
            print("\nSecurity issue status:")
            security_status = security_issues['Status'].value_counts()
            for status, count in security_status.items():
                print(f"  {status}: {count}")
        print()
        
        # Fixed vs New issues
        print("✅ PROGRESS ANALYSIS")
        print("-" * 40)
        fixed_count = len(df[df['Status'] == 'Fixed'])
        new_count = len(df[df['Status'] == 'New'])
        total_actionable = fixed_count + new_count
        
        if total_actionable > 0:
            fix_rate = (fixed_count / total_actionable) * 100
            print(f"Fixed issues: {fixed_count}")
            print(f"New issues: {new_count}")
            print(f"Fix rate: {fix_rate:.1f}%")
        print()
        
        # Component analysis
        print("🏗️ COMPONENT BREAKDOWN")
        print("-" * 40)
        component_counts = df['Component'].value_counts()
        for component, count in component_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{component}: {count} ({percentage:.1f}%)")
        print()
        
        # Action analysis
        print("⚡ ACTION BREAKDOWN")
        print("-" * 40)
        action_counts = df['Action'].value_counts()
        for action, count in action_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{action}: {count} ({percentage:.1f}%)")
        print()
        
        return df
        
    except Exception as e:
        print(f"Error analyzing CSV file: {e}")
        return None

def main():
    csv_file = "secure coding all.csv"
    
    if not os.path.exists(csv_file):
        print(f"CSV file {csv_file} not found")
        return
    
    df = analyze_secure_coding_csv(csv_file)
    
    if df is not None:
        print("=" * 80)
        print("ANALYSIS COMPLETE")
        print("=" * 80)
        print(f"Total issues analyzed: {len(df)}")
        print("For detailed analysis of specific checkers or files,")
        print("you can filter the data further.")

if __name__ == "__main__":
    main()
