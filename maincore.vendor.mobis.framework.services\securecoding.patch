diff --git a/java/com/hkmc/broadcast/BroadcastController.java b/java/com/hkmc/broadcast/BroadcastController.java
index 76a3788..a558f51 100644
--- a/java/com/hkmc/broadcast/BroadcastController.java
+++ b/java/com/hkmc/broadcast/BroadcastController.java
@@ -343,9 +343,22 @@ public class BroadcastController {
     }
 
     void writeToTTY(byte [] packet) {
+        // Check packet is not null before proceeding
+        if (packet == null) {
+            Slog.w(TAG, "Cannot write to TTY: packet is null");
+            return;
+        }
+
         // possible concurrent accesses:
         // binder calls(different threads from thread-pool) + BroadcostThreadRunnable
         if (mTTYOutputStream == null) {
+            Slog.w(TAG, "Cannot write to TTY: mTTYOutputStream is null");
+            return;
+        }
+
+        // Check write lock is not null before synchronizing
+        if (mTTYWriteLock == null) {
+            Slog.w(TAG, "Cannot write to TTY: mTTYWriteLock is null");
             return;
         }
 
@@ -358,7 +371,13 @@ public class BroadcastController {
                     int index = 0;
                     while (len > 0) {
                         int written = len > 8 ? 8 : len;
-                        mTTYOutputStream.write(packet, index, written);
+                        // Additional null check for output stream before writing
+                        if (mTTYOutputStream != null) {
+                            mTTYOutputStream.write(packet, index, written);
+                        } else {
+                            Slog.w(TAG, "TTY output stream became null during write operation");
+                            break;
+                        }
                         try {
                             Thread.sleep(1);
                         }
@@ -369,7 +388,12 @@ public class BroadcastController {
                     }
                 }
                 else {
-                    mTTYOutputStream.write(packet, 0, packet.length);
+                    // Additional null check for output stream before writing
+                    if (mTTYOutputStream != null) {
+                        mTTYOutputStream.write(packet, 0, packet.length);
+                    } else {
+                        Slog.w(TAG, "TTY output stream is null during direct write operation");
+                    }
                 }
             }
             catch (IOException e) {
diff --git a/java/com/hkmc/bypass/CcncCanSignalHandle.java b/java/com/hkmc/bypass/CcncCanSignalHandle.java
index 3805035..1ba3ad1 100644
--- a/java/com/hkmc/bypass/CcncCanSignalHandle.java
+++ b/java/com/hkmc/bypass/CcncCanSignalHandle.java
@@ -156,9 +156,32 @@ class CcncCanSignalHandle {
         Log.d(TAG,"sendHuOptionInfoSignal()");
         mHuOptionInfoValueToSend = 0;
         if (mPlatform.equals(VARIANT_SW_PLATFORM_CANDIDATE_CCNC)) {
-            mHuOptionInfoValueToSend = (mIsNaviSupported * 0x01) + (mIsUsmSupported * 0x02) + (mIsTmsSupported * 0x04) +
-                    (mIsMultiProfileSupported * 0x08) + (mIsCamWarnTextSupported * 0x10) + (mIsLvdsInterfaceNonMipiSupported * 0x20) +
-                    (mIsWelcomeAniSupported * 0x40) + (mIsDriveModeSupported * 0x80)  + (mIsAutoBrightnessSupported * 0x100 + mPlatformType*0x400);
+            // Use safe bit operations to avoid multiplication overflow, as these are bit flags
+            try {
+                int value = 0;
+
+                // Use bit operations instead of multiplication, safer and more efficient
+                if (mIsNaviSupported != 0) value |= 0x01;
+                if (mIsUsmSupported != 0) value |= 0x02;
+                if (mIsTmsSupported != 0) value |= 0x04;
+                if (mIsMultiProfileSupported != 0) value |= 0x08;
+                if (mIsCamWarnTextSupported != 0) value |= 0x10;
+                if (mIsLvdsInterfaceNonMipiSupported != 0) value |= 0x20;
+                if (mIsWelcomeAniSupported != 0) value |= 0x40;
+                if (mIsDriveModeSupported != 0) value |= 0x80;
+                if (mIsAutoBrightnessSupported != 0) value |= 0x100;
+
+                // Safe platform type bit shift operation
+                int platformBits = Math.multiplyExact(mPlatformType, 0x400);
+                value = Math.addExact(value, platformBits);
+
+                mHuOptionInfoValueToSend = value;
+
+            } catch (ArithmeticException e) {
+                Log.e(TAG, "Integer overflow in HU option info calculation", e);
+                mHuOptionInfoValueToSend = 0; // Use safe default value
+            }
+
             Log.d(TAG,"sendHuOptionInfoSignal() mHuOptionInfoValueToSend : " + mHuOptionInfoValueToSend);
         }
         if (mCcncCanSignalService.setIntPropertyValue(VendorCcncVehicleProperty.CAN_HU_OPTIONINFO, 0, mHuOptionInfoValueToSend)) {
diff --git a/java/com/hkmc/media/cdp/mitsubishi/CDPacketParser.java b/java/com/hkmc/media/cdp/mitsubishi/CDPacketParser.java
index a66fe95..9c7e499 100644
--- a/java/com/hkmc/media/cdp/mitsubishi/CDPacketParser.java
+++ b/java/com/hkmc/media/cdp/mitsubishi/CDPacketParser.java
@@ -346,9 +346,23 @@ public class CDPacketParser {
     }
     
     public byte getChecksum(byte[] data){
-        byte checksum =0;        
-        for(int i = 3; i< (data.length-4);i++) {
-            checksum += data[i];
+        if(data == null || data.length < 7) {
+            Slog.e(TAG, "Invalid data for checksum calculation: " +
+                   (data == null ? "null" : "length=" + data.length));
+            return 0;
+        }
+
+        byte checksum = 0;
+        int endIndex = data.length - 4;
+        for(int i = 3; i < endIndex; i++) {
+            // Check array bounds (although loop condition already guarantees, but for safety)
+            if(i >= 0 && i < data.length) {
+                checksum += data[i];
+            } else {
+                Slog.e(TAG, "Array index out of bounds in checksum: " + i +
+                       ", array length: " + data.length);
+                break;
+            }
         }
         return checksum;
     }
@@ -382,11 +396,42 @@ public class CDPacketParser {
     }
     
     private byte[] getPacket(byte[] packetData, int startIndex) {
-        
-        if((startIndex +TTY_COMMAND_BASE_SIZE) > packetData.length) return null;
-        
-        int packetLength = (TTY_START_ORDER.length + packetData[startIndex+3] +TTY_END_ORDER.length +1);
-        if((startIndex + packetLength) > packetData.length ) return null;
+
+        // Safe addition check to prevent integer overflow
+        try {
+            int commandEndIndex = Math.addExact(startIndex, TTY_COMMAND_BASE_SIZE);
+            if(commandEndIndex > packetData.length) return null;
+        } catch (ArithmeticException e) {
+            Slog.e(TAG, "Integer overflow in startIndex + TTY_COMMAND_BASE_SIZE", e);
+            return null;
+        }
+
+        // Check array access bounds
+        int dataIndex = startIndex + 3;
+        if(dataIndex < 0 || dataIndex >= packetData.length) {
+            Slog.e(TAG, "Array index out of bounds: " + dataIndex + ", array length: " + packetData.length);
+            return null;
+        }
+
+        // Safe length calculation to prevent integer overflow
+        int packetLength;
+        try {
+            packetLength = Math.addExact(TTY_START_ORDER.length,
+                          Math.addExact(packetData[dataIndex] & 0xFF,
+                          Math.addExact(TTY_END_ORDER.length, 1)));
+        } catch (ArithmeticException e) {
+            Slog.e(TAG, "Integer overflow in packet length calculation", e);
+            return null;
+        }
+
+        // Safe index calculation to prevent integer overflow
+        try {
+            int packetEndIndex = Math.addExact(startIndex, packetLength);
+            if(packetEndIndex > packetData.length) return null;
+        } catch (ArithmeticException e) {
+            Slog.e(TAG, "Integer overflow in startIndex + packetLength", e);
+            return null;
+        }
         
         if(packetLength < 0){
             Slog.e(TAG, "getPacket - packetLength error : " + packetLength);
diff --git a/java/com/hkmc/micom/BufferPool.java b/java/com/hkmc/micom/BufferPool.java
index d96d944..be9eb1b 100644
--- a/java/com/hkmc/micom/BufferPool.java
+++ b/java/com/hkmc/micom/BufferPool.java
@@ -65,7 +65,15 @@ public class BufferPool {
                 int k = sByteBufferArray.keyAt(i);
                 LinkedList<byte[]> list = sByteBufferArray.valueAt(i);
                 pw.println("   size: " + k + "  buffer counts: " + list.size());
-                total += k * list.size();
+
+                // Safe multiplication operation to prevent integer overflow
+                try {
+                    int bufferSize = Math.multiplyExact(k, list.size());
+                    total = Math.addExact(total, bufferSize);
+                } catch (ArithmeticException e) {
+                    pw.println("   Warning: Integer overflow in buffer size calculation for size " + k);
+                    // Continue processing other buffers, don't interrupt the entire dump process
+                }
             }
         }
         pw.println("  total buffer size: " + total + " byte");
diff --git a/java/com/hkmc/micom/ClusterService.java b/java/com/hkmc/micom/ClusterService.java
index 628ff4e..0fb5cd9 100644
--- a/java/com/hkmc/micom/ClusterService.java
+++ b/java/com/hkmc/micom/ClusterService.java
@@ -1635,6 +1635,12 @@ class ClusterService extends IClusterService.Stub implements MicomComponentBase
      *   MMCAN 으로 HU_OpState 값을 정상적으로 송출을 하더라도 Invalid 값(0x7F) 으로 남아있을 수 있다.)
      */
     private void restoreClusterLastInfo() {
+        // Check power service is not null before calling method
+        if (mMicomPowerService == null) {
+            Slog.w(TAG, "Cannot restore cluster last info: mMicomPowerService is null");
+            return;
+        }
+
         int avPowerStatus = mMicomPowerService.getAvPowerStatus();
         Slog.d(TAG, "restoreClusterLastInfo()::avPowerStatus = " + avPowerStatus + " / mIsTMUCalling = " + mIsTMUCalling + " / mIsCalling = " + mIsCalling);
 
diff --git a/java/com/hkmc/micom/MicomAudioService.java b/java/com/hkmc/micom/MicomAudioService.java
index 3e2d279..c95f780 100644
--- a/java/com/hkmc/micom/MicomAudioService.java
+++ b/java/com/hkmc/micom/MicomAudioService.java
@@ -586,23 +586,40 @@ public class MicomAudioService extends IMicomAudioService.Stub implements MicomC
 
         public void applyJson(HashMap<String, String> keyValue) {
             try {
-                for (String key : keyValue.keySet()) {
-                    if (key.equals("slot")) createVolumeSlots(Integer.parseInt(keyValue.get(key)));
-                    if (key.equals("defaultLevel")) {
-                        if (Integer.parseInt(keyValue.get("maxStep")) == MicomVolumePolicyService.getMaxStep().getMaxVolume()) {
-                            mDefaultLevel = Integer.parseInt(keyValue.get(key));
+                // Check keyValue is not null before iterating
+                if (keyValue != null) {
+                    for (String key : keyValue.keySet()) {
+                        // Check key is not null before using equals
+                        if (key != null && key.equals("slot")) {
+                            String value = keyValue.get(key);
+                            if (value != null) {
+                                createVolumeSlots(Integer.parseInt(value));
+                            }
                         }
-                    }
-                    if (key.equals("maxLevel")) {
-                        if (Integer.parseInt(keyValue.get("maxStep")) == MicomVolumePolicyService.getMaxStep().getMaxVolume()) {
-                            mMaxLevel = Integer.parseInt(keyValue.get(key));
+                        if (key != null && key.equals("defaultLevel")) {
+                            String maxStepValue = keyValue.get("maxStep");
+                            String defaultLevelValue = keyValue.get(key);
+                            if (maxStepValue != null && defaultLevelValue != null) {
+                                if (Integer.parseInt(maxStepValue) == MicomVolumePolicyService.getMaxStep().getMaxVolume()) {
+                                    mDefaultLevel = Integer.parseInt(defaultLevelValue);
+                                }
+                            }
                         }
-                    }
-                    // AVNG5WIDEH-30305, // AVNG5WIDE-285075
-                    // Modified by thunderSoft start : redmine-19678 modify 20 volume change 45 volume
-                    if((MicomVolumePolicyService.getMaxStep() == MicomAudioManagerConstant.VolumeStep.STEP75) || (mNewVolumeStep45 == 1)) {
-                        if (key.equals("useAlternativeVolume")) {
-                            if ((keyValue.get("useAlternativeVolume")).equals("true")) {
+                        if (key != null && key.equals("maxLevel")) {
+                            String maxStepValue = keyValue.get("maxStep");
+                            String maxLevelValue = keyValue.get(key);
+                            if (maxStepValue != null && maxLevelValue != null) {
+                                if (Integer.parseInt(maxStepValue) == MicomVolumePolicyService.getMaxStep().getMaxVolume()) {
+                                    mMaxLevel = Integer.parseInt(maxLevelValue);
+                                }
+                            }
+                        }
+                        // AVNG5WIDEH-30305, // AVNG5WIDE-285075
+                        // Modified by thunderSoft start : redmine-19678 modify 20 volume change 45 volume
+                        if((MicomVolumePolicyService.getMaxStep() == MicomAudioManagerConstant.VolumeStep.STEP75) || (mNewVolumeStep45 == 1)) {
+                            if (key != null && key.equals("useAlternativeVolume")) {
+                                String useAltVolumeValue = keyValue.get("useAlternativeVolume");
+                                if (useAltVolumeValue != null && useAltVolumeValue.equals("true")) {
                                 mUseAlternativeVolume = true;
                                 if (mNewVolumeStep45 == 0) {
                                     mMaxLevel =  Integer.parseInt(keyValue.get("maxVolume"));
@@ -3402,36 +3419,60 @@ public class MicomAudioService extends IMicomAudioService.Stub implements MicomC
                 tmpMicomAudioStatus = mAudioStatusCache.copyClone();
             }
 
+            // Create a copy of listeners to avoid concurrent modification during iteration
+            List<MicomAudioStateChangeBinderListener> listenersCopy;
             synchronized (mStateChangeListeners) {
-                for (int i = mStateChangeListeners.size() - 1; i >= 0; i--) {
-                    MicomAudioStateChangeBinderListener element = mStateChangeListeners.get(i);
-                    try {
-                        element.mListener.onStatusChanged(AudioSetup.FuncCode.RES_GETSTATUS,
-                        tmpMicomAudioStatus);
-                    } catch (RemoteException e) {
-                        if (DBG) {
-                            Slog.e(TAG, "Failed to " + e);
-                        }
-                        mStateChangeListeners.remove(element);
-                        element.mListener.asBinder().unlinkToDeath(element, 0);
+                listenersCopy = new ArrayList<>(mStateChangeListeners);
+            }
+
+            // Iterate over copy and collect failed listeners for removal
+            List<MicomAudioStateChangeBinderListener> failedListeners = new ArrayList<>();
+            for (MicomAudioStateChangeBinderListener element : listenersCopy) {
+                try {
+                    element.mListener.onStatusChanged(AudioSetup.FuncCode.RES_GETSTATUS,
+                    tmpMicomAudioStatus);
+                } catch (RemoteException e) {
+                    if (DBG) {
+                        Slog.e(TAG, "Failed to " + e);
                     }
+                    failedListeners.add(element);
+                    element.mListener.asBinder().unlinkToDeath(element, 0);
+                }
+            }
+
+            // Remove failed listeners atomically
+            if (!failedListeners.isEmpty()) {
+                synchronized (mStateChangeListeners) {
+                    mStateChangeListeners.removeAll(failedListeners);
                 }
             }
         }
 
         private void handleError(byte[] data) {
+            // Create a copy of listeners to avoid concurrent modification during iteration
+            List<MicomAudioStateChangeBinderListener> listenersCopy;
             synchronized (mStateChangeListeners) {
-                for (int i = mStateChangeListeners.size() - 1; i >= 0; i--) {
-                    MicomAudioStateChangeBinderListener element = mStateChangeListeners.get(i);
-                    try {
-                        element.mListener.onError(data[1], data[0]);
-                    } catch (RemoteException e) {
-                        if (DBG) {
-                            Slog.e(TAG, "Failed to " + e);
-                        }
-                        mStateChangeListeners.remove(element);
-                        element.mListener.asBinder().unlinkToDeath(element, 0);
+                listenersCopy = new ArrayList<>(mStateChangeListeners);
+            }
+
+            // Iterate over copy and collect failed listeners for removal
+            List<MicomAudioStateChangeBinderListener> failedListeners = new ArrayList<>();
+            for (MicomAudioStateChangeBinderListener element : listenersCopy) {
+                try {
+                    element.mListener.onError(data[1], data[0]);
+                } catch (RemoteException e) {
+                    if (DBG) {
+                        Slog.e(TAG, "Failed to " + e);
                     }
+                    failedListeners.add(element);
+                    element.mListener.asBinder().unlinkToDeath(element, 0);
+                }
+            }
+
+            // Remove failed listeners atomically
+            if (!failedListeners.isEmpty()) {
+                synchronized (mStateChangeListeners) {
+                    mStateChangeListeners.removeAll(failedListeners);
                 }
             }
         }
diff --git a/java/com/hkmc/micom/MicomVolumePanel.java b/java/com/hkmc/micom/MicomVolumePanel.java
index 5b19651..b215fa6 100644
--- a/java/com/hkmc/micom/MicomVolumePanel.java
+++ b/java/com/hkmc/micom/MicomVolumePanel.java
@@ -1047,6 +1047,11 @@ class MicomVolumePanel extends Handler {
             //mVolLevel.setProgressDrawable(mContext.getResources().getDrawable(mVolLevelRarray.getResourceId(volume, 0)));
             int vaildMaxVol = maxVol;
             while (mVolLevelDrawable.get(vaildMaxVol) == null) {
+                // Safe increment operation to prevent integer overflow
+                if (vaildMaxVol >= Integer.MAX_VALUE) {
+                    Log.e(TAG, "Volume level overflow detected, breaking loop");
+                    break;
+                }
                 vaildMaxVol++;
                 if (vaildMaxVol == 75) break;
             }
diff --git a/java/com/hkmc/micom/ModeManagerService.java b/java/com/hkmc/micom/ModeManagerService.java
index 75ef1f5..06ba6f7 100644
--- a/java/com/hkmc/micom/ModeManagerService.java
+++ b/java/com/hkmc/micom/ModeManagerService.java
@@ -2616,19 +2616,22 @@ public class ModeManagerService extends IModeManager.Stub implements MicomCompon
             @Override
             public void onCameraStatus(boolean front, boolean rear) {
                 if (DBG) Slog.v(TAG, "onCameraStatus(PGSService) front: " + front +",rear:" + rear);
-                if (mIsFrontView != front || mIsRearView != rear) {
-                    // Modified by thundersoft start: when rear camera on and current mode is IQIYI，need goto av off , for CHNP-5011
-                    if(((front || rear) && getCurrentMode() == ModeType.IQIYI) && !mMicomPowerService.isAvOff()){
-                        Slog.d(TAG, "when rear camera on and current mode is IQIYI,need goto av off ");
-                        setAVOff(AVOffReason.MEDIA_DISCONNECT);
-                    }
-                    //modified by thundersoft end
-                    if (mIsRearView != rear) {
-                        mIsRearView = rear;
-                        ModeManagerService.this.onRearViewStatusChanged(rear);
+                // VNA-003 fix: Ensure atomic operation for camera status changes
+                synchronized (mModeManagerLock) {
+                    if (mIsFrontView != front || mIsRearView != rear) {
+                        // Modified by thundersoft start: when rear camera on and current mode is IQIYI，need goto av off , for CHNP-5011
+                        if(((front || rear) && getCurrentMode() == ModeType.IQIYI) && !mMicomPowerService.isAvOff()){
+                            Slog.d(TAG, "when rear camera on and current mode is IQIYI,need goto av off ");
+                            setAVOff(AVOffReason.MEDIA_DISCONNECT);
+                        }
+                        //modified by thundersoft end
+                        if (mIsRearView != rear) {
+                            mIsRearView = rear;
+                            ModeManagerService.this.onRearViewStatusChanged(rear);
+                        }
+                        mIsFrontView = front;
+                        ModeManagerService.this.onCameraViewStatusChanged(front,rear);
                     }
-                    mIsFrontView = front;
-                    ModeManagerService.this.onCameraViewStatusChanged(front,rear);
                 }
             }
         });
diff --git a/java/com/hkmc/micom/PgsService.java b/java/com/hkmc/micom/PgsService.java
index ed5d801..9adb780 100644
--- a/java/com/hkmc/micom/PgsService.java
+++ b/java/com/hkmc/micom/PgsService.java
@@ -1797,14 +1797,20 @@ final class PgsService extends IPgsService.Stub implements MicomComponentBase {
                                     // DRVM customkey is pressed while rear view(gearR) is showing
                                     showReqCameraOffOsd();
                                 } else if (!mIsIGNOn || mIsGearP) {
-                                    dismissDrvmPopup(false);
-                                    showDrvmPopup();
+                                    // VNA-003 fix: Ensure atomic operation for popup dismiss and show
+                                    synchronized (mPgsServiceLock) {
+                                        dismissDrvmPopup(false);
+                                        showDrvmPopup();
+                                    }
                                 }
                             }
                         }
                     } else {
-                        dismissDrvmPopup(false);
-                        showDrvmPopup();
+                        // VNA-003 fix: Ensure atomic operation for popup dismiss and show
+                        synchronized (mPgsServiceLock) {
+                            dismissDrvmPopup(false);
+                            showDrvmPopup();
+                        }
                     }
                 } else if (action.equals(PgsConstants.IntentDefine.ACTION_DEVICE_STORAGE_LOW)) {
                     myLogD("ACTION_DEVICE_STORAGE_LOW intent received");
diff --git a/java/com/hkmc/micom/PopupController.java b/java/com/hkmc/micom/PopupController.java
index d28da4a..f05140e 100644
--- a/java/com/hkmc/micom/PopupController.java
+++ b/java/com/hkmc/micom/PopupController.java
@@ -171,39 +171,75 @@ import com.android.internal.R;
         mHandler.sendMessage(msg);
     }
     private void onShowRegDialog(String msg){
-        if (mDialog != null) {
-            if (mRegulationService != null) {
-                if(mRegulationService.isReverseOn()){
-                    if (DBG) Slog.v(TAG, "Skip showing System Regulation PopUp due to reverse on!!");
-                    return;
-                }
+        // Check for null dialog before proceeding
+        if (mDialog == null) {
+            Slog.w(TAG, "Cannot show regulation dialog: mDialog is null");
+            return;
+        }
+
+        // Check for null message parameter
+        if (msg == null) {
+            Slog.w(TAG, "Cannot show regulation dialog: message is null");
+            return;
+        }
+
+        // Check regulation service and reverse status
+        if (mRegulationService != null) {
+            if(mRegulationService.isReverseOn()){
+                if (DBG) Slog.v(TAG, "Skip showing System Regulation PopUp due to reverse on!!");
+                return;
             }
-            if (DBG) Slog.v(TAG, "Show System Regulation PopUp");
-            mDialog.setMessage(msg);
-            mDialog.show();
-            mRegPopupShowingRequestTime = SystemClock.uptimeMillis();
+        }
+
+        if (DBG) Slog.v(TAG, "Show System Regulation PopUp");
+        mDialog.setMessage(msg);
+        mDialog.show();
+        mRegPopupShowingRequestTime = SystemClock.uptimeMillis();
+
+        // Check handler is not null before sending message
+        if (mHandler != null) {
             mHandler.sendEmptyMessageDelayed(MSG_DISMISSREGULATION_POPUP, 10000);   // 150106 <EMAIL> - change timeout 5s -> 10s
         }
     }
     public void dismissRegulationDialog() {
-        mHandler.removeMessages(MSG_DISMISSREGULATION_POPUP);
-        mHandler.removeMessages(MSG_REGULATION_POPUP);
+        // Check handler is not null before removing messages
+        if (mHandler != null) {
+            mHandler.removeMessages(MSG_DISMISSREGULATION_POPUP);
+            mHandler.removeMessages(MSG_REGULATION_POPUP);
+        }
 
+        // Check dialog is not null and is showing before dismissing
         if (mDialog != null && mDialog.isShowing()) {
             long timePassedAfterShowingRegPopup = SystemClock.uptimeMillis() - mRegPopupShowingRequestTime;
             if ( timePassedAfterShowingRegPopup < 500) {
                 if (DBG) Slog.v(TAG, "time passed after showing Reg Popup: "+timePassedAfterShowingRegPopup+", send message delayed: "+(500 - timePassedAfterShowingRegPopup));
-                mHandler.sendEmptyMessageDelayed(MSG_DISMISSREGULATION_POPUP, 500 - timePassedAfterShowingRegPopup);
+                // Additional null check for handler before sending delayed message
+                if (mHandler != null) {
+                    mHandler.sendEmptyMessageDelayed(MSG_DISMISSREGULATION_POPUP, 500 - timePassedAfterShowingRegPopup);
+                }
             }else {
                 if (DBG) Slog.v(TAG, "time passed after showing Reg Popup: "+timePassedAfterShowingRegPopup);
-                mHandler.sendEmptyMessage(MSG_DISMISSREGULATION_POPUP);
+                // Additional null check for handler before sending message
+                if (mHandler != null) {
+                    mHandler.sendEmptyMessage(MSG_DISMISSREGULATION_POPUP);
+                }
             }
         }
     }
     private void onDismissRegDialog(){
-        if (mDialog != null && mDialog.isShowing()) {
-            if (DBG) Slog.v(TAG, "Dismiss System Regulation PopUp");
-            mDialog.dismiss();
+        // Check dialog is not null before checking if showing
+        if (mDialog != null) {
+            try {
+                if (mDialog.isShowing()) {
+                    if (DBG) Slog.v(TAG, "Dismiss System Regulation PopUp");
+                    mDialog.dismiss();
+                }
+            } catch (Exception e) {
+                // Handle potential exceptions during dialog operations
+                Slog.e(TAG, "Error dismissing regulation dialog", e);
+            }
+        } else {
+            if (DBG) Slog.v(TAG, "Cannot dismiss regulation dialog: mDialog is null");
         }
     }
 }
diff --git a/java/com/hkmc/micom/SystemService.java b/java/com/hkmc/micom/SystemService.java
index 381f993..8abaefd 100644
--- a/java/com/hkmc/micom/SystemService.java
+++ b/java/com/hkmc/micom/SystemService.java
@@ -2806,22 +2806,23 @@ public class SystemService extends ISystemService.Stub implements MicomComponent
         //if (mIsAltLOn != alt) {
         //    mIsAltLOn = alt;
         if (DBG) Slog.d(TAG, "[onAlternateStatus] before: " + mIsAltLOn.get() + ", after: "+alt);
-        if (mIsAltLOn.get() != alt) {
-            mIsAltLOn.set(alt);
-            if (DBG)
-                Slog.v(TAG, "ALTERNATE status changed to : " + alt);
-            mContext.removeStickyBroadcast(mIntentAltLOn);
-            mContext.removeStickyBroadcast(mIntentAltLOff);
-            mContext.sendStickyBroadcast(alt ? mIntentAltLOn : mIntentAltLOff);
+        // VNA03-J: Ensure atomic operation - check, set, and notification must be atomic
+        synchronized (mSystemServiceLock) {
+            if (mIsAltLOn.get() != alt) {
+                mIsAltLOn.set(alt);
+                if (DBG)
+                    Slog.v(TAG, "ALTERNATE status changed to : " + alt);
+                mContext.removeStickyBroadcast(mIntentAltLOn);
+                mContext.removeStickyBroadcast(mIntentAltLOff);
+                mContext.sendStickyBroadcast(alt ? mIntentAltLOn : mIntentAltLOff);
 
-            Intent intent = new Intent(VehicleInfoManager.ACTION_ALTERNATE_STATUS_CHANGED);
-            intent.putExtra(VehicleInfoManager.EXTRA_ALTERNATE_STATUS, alt);
-            mContext.sendBroadcast(intent, BroadcastPermission.CCNC_BASIC_CUSTOM);
+                Intent intent = new Intent(VehicleInfoManager.ACTION_ALTERNATE_STATUS_CHANGED);
+                intent.putExtra(VehicleInfoManager.EXTRA_ALTERNATE_STATUS, alt);
+                mContext.sendBroadcast(intent, BroadcastPermission.CCNC_BASIC_CUSTOM);
 
-            //EventLog.writeEvent(EventLogTags.IVI_ALT_STATE, mIsAltLOn ? 1 : 0, SystemClock.uptimeMillis());
-            EventLog.writeEvent(EventLogTags.IVI_ALT_STATE, mIsAltLOn.get() ? 1 : 0, SystemClock.uptimeMillis());
+                //EventLog.writeEvent(EventLogTags.IVI_ALT_STATE, mIsAltLOn ? 1 : 0, SystemClock.uptimeMillis());
+                EventLog.writeEvent(EventLogTags.IVI_ALT_STATE, alt ? 1 : 0, SystemClock.uptimeMillis());
 
-            synchronized (mSystemServiceLock) {
                 for (SystemServiceBinderListener bl : mListeners) {
                     try {
                         bl.mListener.onAltLStatus(alt);
@@ -2829,6 +2830,7 @@ public class SystemService extends ISystemService.Stub implements MicomComponent
                     }
                 }
             }
+        }
 
             checkDischargeWarningCondition();
 
diff --git a/java/com/hkmc/micom/VideoSetupService.java b/java/com/hkmc/micom/VideoSetupService.java
index d2501aa..8527f9d 100644
--- a/java/com/hkmc/micom/VideoSetupService.java
+++ b/java/com/hkmc/micom/VideoSetupService.java
@@ -1649,9 +1649,12 @@ class VideoSetupService extends IVideoSetupService.Stub implements MicomComponen
     @Override
     public void enterDimmingBrightnessSettingActivity(){
         if(DBG) Slog.v(TAG, "Entered display Setting Activity");
-        //mIsDimmingActivityOn  = true;
-        mIsDimmingActivityOn.set(true);
-        notifyAutolightDimmingOnOff(false);
+        // VNA03-J: Ensure atomic operation - setting state and notification must be atomic
+        synchronized(mVideoSetupLock) {
+            //mIsDimmingActivityOn  = true;
+            mIsDimmingActivityOn.set(true);
+            notifyAutolightDimmingOnOff(false);
+        }
     }
     /**
      * inform VideoSetupService about exit Setting Activity
@@ -1659,18 +1662,21 @@ class VideoSetupService extends IVideoSetupService.Stub implements MicomComponen
     @Override
     public void exitDimmingBrightnessSettingActivity(){
         if(DBG) Slog.v(TAG, "exit display setting Activity");
-        //mIsDimmingActivityOn = false;
-        mIsDimmingActivityOn.set(false);
-        //modified by thundersoft start  AVNG5XC-5660  The CPU sends MON_SET_DIMMING_BRIGHTNESS_C to MICOM when the LCD is on, old:if(VideoSetup.Dim.AUTO == mDim.get()){
-        if(VideoSetup.Dim.AUTO == mDim.get() && getScreenOn(VideoSetup.Type.FRONT) == LcdState.ON) {
-        //modified by thundersoft end
-            if(false == mIlluminateStatus){
-                requestDimmingBrightness(VideoSetup.Dim.NIGHT, mNightDimmingLevel);
-            }else {
-                requestDimmingBrightness(VideoSetup.Dim.DAYLIGHT, mDayDimmingLevel);
+        // VNA03-J: Ensure atomic operation - setting state and subsequent operations must be atomic
+        synchronized(mVideoSetupLock) {
+            //mIsDimmingActivityOn = false;
+            mIsDimmingActivityOn.set(false);
+            //modified by thundersoft start  AVNG5XC-5660  The CPU sends MON_SET_DIMMING_BRIGHTNESS_C to MICOM when the LCD is on, old:if(VideoSetup.Dim.AUTO == mDim.get()){
+            if(VideoSetup.Dim.AUTO == mDim.get() && getScreenOn(VideoSetup.Type.FRONT) == LcdState.ON) {
+            //modified by thundersoft end
+                if(false == mIlluminateStatus){
+                    requestDimmingBrightness(VideoSetup.Dim.NIGHT, mNightDimmingLevel);
+                }else {
+                    requestDimmingBrightness(VideoSetup.Dim.DAYLIGHT, mDayDimmingLevel);
+                }
             }
+            notifyAutolightDimmingOnOff(true);
         }
-        notifyAutolightDimmingOnOff(true);
     }
 
     public void setDimmingDefaultBrightness(int day, int night) {
diff --git a/java/com/hkmc/server/LockScreenService.java b/java/com/hkmc/server/LockScreenService.java
index c5d644a..30e78d9 100644
--- a/java/com/hkmc/server/LockScreenService.java
+++ b/java/com/hkmc/server/LockScreenService.java
@@ -1349,7 +1349,8 @@ final class LockScreenService extends ILockScreen.Stub
         private static final int TYPE_ACN = 2;
         private static final int TYPE_RSA = 3;
 
-        private boolean mInterrupt;
+        // VNA-003 fix: Use volatile to ensure visibility across threads
+        private volatile boolean mInterrupt;
         private int cnt = 0;
         private int mCallTimeHour;
         private int mCallTimeMin;
@@ -1389,10 +1390,15 @@ final class LockScreenService extends ILockScreen.Stub
                 mCallTimeMin = (cnt / 60) % 60;
                 mCallTimeSec = cnt % 60;
 
-                Message message = mHandler.obtainMessage(MSG_CONNECT_TIME_UPDATE);
-                message.obj = this;
-                message.arg1 = mTypeConnect;
-                mHandler.sendMessageDelayed(message, 0);
+                // Check handler is not null before creating and sending message
+                if (mHandler != null) {
+                    Message message = mHandler.obtainMessage(MSG_CONNECT_TIME_UPDATE);
+                    if (message != null) {
+                        message.obj = this;
+                        message.arg1 = mTypeConnect;
+                        mHandler.sendMessageDelayed(message, 0);
+                    }
+                }
 
                 SystemClock.sleep(1000);
             }
@@ -1400,11 +1406,16 @@ final class LockScreenService extends ILockScreen.Stub
     };
 
     private void initConnectTimeThread() {
-        mConnectTimeThread.mInterrupt = false;
-        mConnectTimeThread.cnt = 0;
-        mConnectTimeThread.mCallTimeHour = 0;
-        mConnectTimeThread.mCallTimeMin = 0;
-        mConnectTimeThread.mCallTimeSec = 0;
+        // Check thread object is not null before accessing its fields
+        if (mConnectTimeThread != null) {
+            mConnectTimeThread.mInterrupt = false;
+            mConnectTimeThread.cnt = 0;
+            mConnectTimeThread.mCallTimeHour = 0;
+            mConnectTimeThread.mCallTimeMin = 0;
+            mConnectTimeThread.mCallTimeSec = 0;
+        } else {
+            Slog.w(TAG, "Cannot initialize connect time thread: mConnectTimeThread is null");
+        }
     }
 
     private void startConnectTimeThread(int type) {
diff --git a/java/com/hkmc/server/OsdService.java b/java/com/hkmc/server/OsdService.java
index ce68e21..78f05bb 100644
--- a/java/com/hkmc/server/OsdService.java
+++ b/java/com/hkmc/server/OsdService.java
@@ -325,7 +325,15 @@ class OsdService extends IOsdService.Stub implements IHkmcServiceBase{
     }
 
     private long getCloseTime(OsdRecord r) {
-        return r.closeTime + (r.immediate ? CLOSE_DELAY_IMMEDIATE : CLOSE_DELAY);
+        // Safe addition operation to prevent long integer overflow
+        try {
+            long delay = r.immediate ? CLOSE_DELAY_IMMEDIATE : CLOSE_DELAY;
+            return Math.addExact(r.closeTime, delay);
+        } catch (ArithmeticException e) {
+            Slog.e(TAG, "Long overflow in getCloseTime calculation", e);
+            // Return current time when overflow occurs, indicating immediate close
+            return SystemClock.uptimeMillis();
+        }
     }
 
     private void scheduleFlushLocked(long closeTime) {
@@ -624,8 +632,19 @@ class OsdService extends IOsdService.Stub implements IHkmcServiceBase{
         OsdRecord r = mCanceledRecord;
         if (r != null && r.osd.asBinder() == osd.asBinder()) {
             long now = SystemClock.uptimeMillis();
-            if (!r.immediate && (r.closeTime + CLOSE_DELAY) > (now + CLOSE_DELAY_IMMEDIATE)) {
-                Slog.e(TAG, "updateCloseDelayToImmediateLocked: close time change to immediate. osd=" + r);
+
+            // Safe addition operation to prevent long integer overflow
+            try {
+                long closeTimeWithDelay = Math.addExact(r.closeTime, CLOSE_DELAY);
+                long nowWithImmediateDelay = Math.addExact(now, CLOSE_DELAY_IMMEDIATE);
+
+                if (!r.immediate && closeTimeWithDelay > nowWithImmediateDelay) {
+                    Slog.e(TAG, "updateCloseDelayToImmediateLocked: close time change to immediate. osd=" + r);
+                    scheduleCloseLocked(r, true);
+                }
+            } catch (ArithmeticException e) {
+                Slog.e(TAG, "Long overflow in time calculation", e);
+                // When overflow occurs, set to immediate close
                 scheduleCloseLocked(r, true);
             }
         }
