<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:music="http://schemas.android.com/apk/res/com.daudio.av.app.usbmusic"
    android:layout_width="match_parent"
    android:layout_height="480dip"
    android:gravity="center_horizontal"
    android:background="@color/popup_bg"
    android:layoutDirection="ltr">    

    <FrameLayout
        android:id="@+id/popup_view"
        android:layout_width="840dip"
        android:layout_height="228dip"
        android:layout_marginRight="220dip"
        android:layout_marginTop="78dip">

        <View
            android:id="@+id/popup_bg"
            android:layout_width="840dip"
            android:layout_height="228dip"
            android:background="@drawable/co_bg_popup" />

        <View
            android:id="@+id/popup_normal_icon"
            android:layout_width="46dip"
            android:layout_height="46dip"
            android:layout_marginLeft="397dip"
            android:layout_marginTop="18dip"
            android:background="@drawable/co_ic_info" />

        <TextView
            android:id="@+id/popup_first_message"
            android:layout_width="784dip"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="78dip"
            android:ellipsize="end"
            android:gravity="center"
            android:text="@string/question_popup_confirm_hide"
            android:textColor="@color/popup_message"
            android:textSize="26sp"
            android:lineSpacingExtra="4dip" />

        <LinearLayout
            android:id="@+id/popup_button_layout"
            android:layout_width="824dip"
            android:layout_height="52dip"
            android:layout_marginLeft="8dip"
            android:layout_marginTop="170dip"
            android:orientation="horizontal" >

            <Button
                android:id="@+id/popup_first_button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/music_button_bottom_bg"
                android:gravity="center"
                android:textColor="@color/text_normal"
                android:textSize="26sp" />

            <Button
                android:id="@+id/popup_second_button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginLeft="8dip"
                android:background="@drawable/music_button_bottom_bg"
                android:gravity="center"
                android:textColor="@color/text_normal"
                android:textSize="26sp" />

        </LinearLayout>
    </FrameLayout>

</FrameLayout>
