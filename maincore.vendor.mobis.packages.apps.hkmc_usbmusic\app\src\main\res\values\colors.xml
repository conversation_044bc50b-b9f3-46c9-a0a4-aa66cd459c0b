<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="text_music_title">#FFFFFFFF</color>
    <color name="text_music_list">#FFE4E4E4</color>
    <color name="text_music_info">#FFCCCCCC</color>
    <color name="text_music_info_search">#FF42A3FF</color>
    <color name="text_music_count">#FFFFFFFF</color>
    <color name="text_current_time">#FFFFFFFF</color>
    <color name="text_totaltime">#FFFFFFFF</color>
    <color name="current_time_border">#00000000</color>
    <color name="currentTime_drop_shadow_color">#000000</color>
    <color name="total_time_border">#00000000</color>
    
    <color name="text_list_item_nor">#FFCCCCCC</color>
    
    <color name="text_music_osd_filename_color">#FFFFFFFF</color>
    <color name="text_music_osd_filename_search_color">#FF42A3FF</color>

    <color name="popup_bg">#AA000000</color>
    <color name="popup_message">#FFFFFFFF</color>
    <color name="popup_btn">#FFE4E4E4</color>

    <color name="text_list_indexer_item">#FFFFFFFF</color>
    <color name="text_list_indexer_indicator">#FFFFFFFF</color>
    
    <color name="text_list_tab_nor">#FFFFFFFF</color>
    <color name="text_list_tab_dis">#FF4E4E4E</color>
    
    <color name="text_selected">#FF000000</color>
    <color name="text_disable">#FF4E4E4E</color>
    <color name="text_normal">#FFFFFFFF</color>

    <color name="text_artist">#FFFFFFFF</color>
    <color name="text_album">#FFa1a1a1</color>

    <color name="text_white_color">#FFFFFFFF</color>
    <color name="text_titlebar_button_nor">#FF000000</color>

    <color name="albumart_stroke_color">#312c31</color>

    <color name="music_prepare_string">#FFFFFFFF</color>

</resources>
