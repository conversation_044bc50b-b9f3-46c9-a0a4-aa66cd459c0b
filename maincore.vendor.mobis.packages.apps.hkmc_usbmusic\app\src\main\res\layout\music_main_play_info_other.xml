<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_main_play_info_other.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#       Date(yyyy-mm-dd)           By                  Description
 ******************************************************************************
     1.0               2017-02-03         Samarth Dubey       play info xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="854dip"
    android:layout_height="480dp" >

    <TextView
        android:id="@+id/other_title_name"
        android:layout_width="378dip"
        android:layout_height="60dip"
        android:layout_gravity="left"
        android:layout_marginLeft="70dip"
        android:layout_marginTop="188dip"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical|left"
        android:includeFontPadding="false"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textColor="@color/text_music_title"
        android:textSize="46sp" />

    <TextView
        android:id="@+id/other_artist_name"
        android:layout_width="378dip"
        android:layout_height="34dip"
        android:layout_gravity="left"
        android:layout_marginLeft="70dip"
        android:layout_marginTop="258dip"
        android:ellipsize="end"
        android:gravity="center_vertical|left"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="@color/text_artist"
        android:textSize="24sp" />

    <TextView
        android:id="@+id/other_album_name"
        android:layout_width="378dip"
        android:layout_height="34dip"
        android:layout_gravity="left"
        android:layout_marginLeft="70dip"
        android:layout_marginTop="294dip"
        android:ellipsize="end"
        android:gravity="center_vertical|left"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="@color/text_album"
        android:textSize="24sp" />

</FrameLayout>
