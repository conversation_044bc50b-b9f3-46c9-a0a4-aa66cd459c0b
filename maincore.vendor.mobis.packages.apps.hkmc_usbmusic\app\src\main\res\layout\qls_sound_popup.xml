<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/popuplayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:orientation="vertical" >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="106px"
            android:layout_weight="1" >

            <RadioButton
                android:id="@+id/radiobtn_audience_mode"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/music_list_item_text_bg"
                android:button="@null"
                android:drawableLeft="@drawable/co_radio_selector"
                android:drawablePadding="33dp"
                android:paddingLeft="19dp"
                android:text="@string/QLS_item_1"
                android:textColor="@color/popup_message"
                android:textSize="26sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@drawable/co_bg_list_line"
            android:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="106px"
            android:layout_weight="1" >

            <RadioButton
                android:id="@+id/radiobtn_stage_mode"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/music_list_item_text_bg"
                android:button="@null"
                android:drawableLeft="@drawable/co_radio_selector"
                android:drawablePadding="33dp"
                android:paddingLeft="19dp"
                android:text="@string/QLS_item_2"
                android:textColor="@color/popup_message"
                android:textSize="26sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@drawable/co_bg_list_line"
            android:visibility="visible" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="106px"
            android:layout_weight="1" >

            <RadioButton
                android:id="@+id/radiobtn_off_mode"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/music_list_item_text_bg"
                android:button="@null"
                android:drawableLeft="@drawable/co_radio_selector"
                android:drawablePadding="33dp"
                android:paddingLeft="19dp"
                android:text="@string/QLS_item_3"
                android:textColor="@color/popup_message"
                android:textSize="26sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@drawable/co_bg_list_line"
            android:visibility="visible" />
    </LinearLayout>

</LinearLayout>