<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="388dp"
    android:layout_height="wrap_content">

    <ProgressBar
        android:id="@+id/progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_marginLeft="25dp"
        android:layout_marginTop="19dp"
        android:layout_marginBottom="1dp"
        android:layout_marginRight="25dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
    />

    <TextView
        android:id="@+id/progress_percent"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="12dp"
        android:layout_alignParentLeft="true"
        android:layout_below="@id/progress"
    />

    <TextView
        android:id="@+id/progress_number"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="15dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="12dp"
        android:layout_alignParentLeft="true"
        android:layout_below="@id/progress"
    />
</RelativeLayout>
