<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_main_progress.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#       Date(yyyy-mm-dd)          By              Description
 *****************************************************************************
     1.0               2017-02-03       Samarth Dubey       song progress xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="854dip"
    android:layout_height="480dip" >
    
    <TextView
        android:id="@+id/totaltime"
        android:layout_width="wrap_content"
		android:minWidth="80dp"
        android:layout_height="28dip"
        android:layout_gravity="right|top"
        android:layout_marginRight="12dp"
        android:layout_marginTop="448dip"
        android:gravity="right|center_vertical"
        android:singleLine="true"
        android:textColor="@color/text_totaltime"
        android:textSize="18sp" />
	<!--Modified by thunderSoft start: modify maxHeight-->
	<SeekBar
		android:id="@+id/progressBar"
		android:layout_width="854dip"
		android:layout_height="56dip"
		android:layout_gravity="left|top"
		android:layout_marginLeft="0dip"
		android:layout_marginTop="424dip"
		android:maxHeight="56dp"
		android:minHeight="56dp"
		android:progressDrawable="@drawable/music_progress_horizontal"
		android:thumb="@null" />
	<!--Modified by thunderSoft end-->

    <View
        android:id="@+id/progress_push_bg"
        android:layout_gravity="left"
        android:layout_marginTop="424dip"
        android:layout_marginLeft="2dip"
        android:layout_width="108dip"
        android:layout_height="56dip"
        android:visibility="invisible"
        android:background="@drawable/me_bg_player_progress_press"
    />    

    <TextView
		android:id="@+id/currenttime"
		android:layout_width="80dip"
		android:layout_height="28dip"
		android:layout_marginLeft="0dip"
		android:layout_marginTop="448dip"
		android:gravity="right|center_vertical"
		android:singleLine="true"
		android:textColor="@color/text_current_time"
        android:textSize="18sp" />
    
</FrameLayout>
