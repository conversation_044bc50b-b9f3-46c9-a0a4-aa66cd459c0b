<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_main_play_info.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                              By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                                                     AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#       Date(yyyy-mm-dd)             By                  Description
 ******************************************************************************
     1.0               2017-02-03             Samarth Dubey    play info xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="854dip"
    android:layout_height="480dip" >

    <View
        android:id="@+id/music_album_art_border"
        android:layout_width="235dip"
        android:layout_height="235dip"
        android:layout_gravity="left"
        android:background="@drawable/me_bg_album"
        android:layout_marginLeft="122dip"
        android:layout_marginTop="176dip"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/music_album_fit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left"
        android:adjustViewBounds="true"
        android:layout_marginLeft="124dip"
        android:layout_marginTop="178dip" />

    <View
        android:id="@+id/music_album"
        android:layout_width="231dip"
        android:layout_height="231dip"
        android:layout_gravity="left"
        android:layout_marginLeft="124dip"
        android:layout_marginTop="178dip" />
		
    <com.daudio.av.app.usbmusic.ui.view.main.MusicCircleView
        android:id="@+id/music_circle_album"
        android:layout_width="154dip"
        android:layout_height="154dip"
        android:layout_gravity="left"
        android:layout_marginLeft="121dip"
        android:layout_marginTop="226dip"
		android:visibility="gone"/>

    <View
        android:id="@+id/music_album_art_mask"
        android:layout_width="235dip"
        android:layout_height="235dip"
        android:layout_gravity="left"
        android:layout_marginLeft="82dip"
        android:layout_marginTop="176dip"
        android:visibility="gone"/>

    <View
        android:id="@+id/music_album_push"
        android:layout_width="231dip"
        android:layout_height="231dip"
        android:layout_gravity="left"
        android:layout_marginLeft="124dip"
        android:layout_marginTop="178dip" />

    <include
        android:id="@+id/play_info_other"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        layout="@layout/music_main_play_info_other" />


    <FrameLayout
        android:layout_width="854dip"
        android:layout_height="40dip"
        android:layout_gravity="left|top"
        android:layout_marginTop="124dip">

        <LinearLayout
        android:layout_width="854dip"
        android:layout_height="40dip"
        android:layout_gravity="left|top"
        android:gravity="left|center_vertical" >        

        <TextView
        android:id="@+id/music_current_index"
        android:layout_width="wrap_content"
        android:layout_height="40dip"
        android:layout_marginLeft="32dip"
        android:textColor="@color/text_music_count"
        android:gravity="center_vertical"  
        android:layoutDirection="ltr"
        android:textSize="18sp"
        android:visibility="gone" />

        <TextView
        android:id="@+id/music_total_count"
        android:layout_width="wrap_content"
        android:layout_height="40dip"
        android:gravity="center_vertical"
        android:layoutDirection="ltr"
        android:textColor="@color/text_music_count"
        android:textSize="18sp" />

        <View
        android:id="@+id/music_icon"
        android:layout_width="40dip"
        android:layout_height="40dip"
        android:background="@drawable/me_ic_play_song"
        android:gravity="center_vertical"      
        android:visibility="gone"/>

        </LinearLayout>

    </FrameLayout>

</FrameLayout>
