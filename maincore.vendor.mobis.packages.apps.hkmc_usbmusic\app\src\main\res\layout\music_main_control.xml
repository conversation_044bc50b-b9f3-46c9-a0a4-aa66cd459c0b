<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_list_control.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#      Date(yyyy-mm-dd)        By                  Description
 ******************************************************************************
     1.0            2017-02-03         Samarth Dubey    main view controls xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:music="http://schemas.android.com/apk/res/com.daudio.av.app.usbmusic"
    android:layout_width="854dip"
    android:layout_height="480dip" >

    <LinearLayout
        android:id="@+id/prev_play_next"
        android:layout_width="408dp"
        android:layout_height="80dp"
        android:layout_marginStart="24dp"
        android:layout_marginTop="336dip"
        android:layout_gravity="left|top"
        android:orientation="horizontal" >

        <com.daudio.av.app.usbmusic.ui.widget.button.MediaRepeatButton
            android:id="@+id/prev"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:onClick="onMusicControlClick"
            android:layout_weight="1"
            android:background="@drawable/music_control_prev_bg"
            music:repeatInterval="200" />

        <ImageButton
            android:id="@+id/play_pause"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:background="@drawable/music_control_play_bg"
            android:onClick="onMusicControlClick" />

        <com.daudio.av.app.usbmusic.ui.widget.button.MediaRepeatButton
            android:id="@+id/next"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:background="@drawable/music_control_next_bg"
            android:onClick="onMusicControlClick"
            android:layout_weight="1"
            music:repeatInterval="200" />

        <ImageButton
            android:id="@+id/thumbup"
            android:layout_width="90dp"
            android:layout_height="80dp"
            android:src="@drawable/music_control_thumbup_bg"/>
            android:background="@null"
            android:onClick="onMusicControlClick"
            android:visibility="gone" />

    </LinearLayout>


    <com.daudio.av.app.usbmusic.ui.widget.button.MediaButton
        android:id="@+id/repeat"
        android:layout_width="80dip"
        android:layout_height="68dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="504dip"
        android:layout_marginTop="184dip"
        android:onClick="onMusicControlClick" />

    <com.daudio.av.app.usbmusic.ui.widget.button.MediaButton
        android:id="@+id/shuffle"
        android:layout_width="80dip"
        android:layout_height="68dip"
        android:layout_gravity="left"
        android:layout_marginLeft="504dip"
        android:layout_marginTop="262dip"
        android:onClick="onMusicControlClick" />

    <com.daudio.av.app.usbmusic.ui.widget.button.MediaButton
        android:id="@+id/delete"
        android:layout_width="80dip"
        android:layout_height="68dip"
        android:layout_gravity="left"
        android:layout_marginLeft="504dip"
        android:layout_marginTop="340dip"
        android:background="@drawable/music_control_hide_bg"
        android:onClick="onMusicControlClick"
        android:visibility="gone"/>

</FrameLayout>
