<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_main_list_tab_item.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#      Date(yyyy-mm-dd)          By                  Description
 ******************************************************************************
     1.0           2017-02-03           Samarth Dubey    main view list tab item xml file
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:music="http://schemas.android.com/apk/res/com.daudio.av.app.usbmusic"
    android:layout_width="match_parent"
    android:layout_height="72dip" >

    <View
        android:layout_width="match_parent"
        android:layout_height="1.3dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="0dip"
        android:layout_marginTop="70.7dip"
        android:background="@drawable/co_bg_list_line" />

    <com.daudio.av.app.usbmusic.ui.widget.list.MusicListItem
        android:id="@+id/list_item_text"
        android:layout_width="match_parent"
        android:layout_height="72dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="0dip"
        android:layout_marginTop="0dip"
        android:background="@drawable/music_list_item_text_bg"
        android:ellipsize="marquee"        
        android:focusable="true"
        android:gravity="center_vertical|right"
        android:marqueeRepeatLimit="marquee_forever"
		android:layoutDirection="rtl"
        android:paddingLeft="24dip"
        android:paddingRight="72dip"
        android:paddingTop="0dip"
        android:singleLine="true"
        android:textColor="@drawable/music_list_item_bg_color"
        android:textSize="26sp"
        music:focusImage="@drawable/co_btn_bg_list_focus"
        music:folderImage="@drawable/music_list_item_icon_folder"
        music:iconImageHeight="40dip"
        music:iconImageLeft="364dip"
        music:iconImageTop="16dip"
        music:iconImageWidth="40dip"
        music:isCheckable="false"
        music:resizable="false"
        music:songPauseImage="@drawable/music_list_item_icon_song_pause"
        music:songPlayImage="@drawable/music_list_item_icon_song_play"
        music:songPlayAnimationImage="@drawable/music_list_item_icon_song_play_animation"
        music:songPauseAnimationImage="@drawable/music_list_item_icon_song_pause_animation"
        music:folderPlayAnimationImage="@drawable/music_list_item_icon_folder_play_animation"
        music:folderPauseAnimationImage="@drawable/music_list_item_icon_folder_pause_animation"
        music:userListPauseImage="@drawable/music_list_item_icon_user_list_pause"
        music:userListPlayImage="@drawable/music_list_item_icon_user_list_play" />


    <View
        android:id="@+id/list_item_extra_image"
        android:layout_width="40dip"
        android:layout_height="40dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="364dip"
        android:layout_marginTop="16dip"
        android:background="@drawable/me_ic_equalizer_sel" />

</FrameLayout>
