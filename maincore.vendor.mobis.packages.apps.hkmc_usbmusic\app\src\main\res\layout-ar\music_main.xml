<?xml version="1.0" encoding="utf-8"?>
<!--
 ***************************************************************************
 ** (C) 2017 Hyundai MOBIS
 **     All rights reserved.
 *****************************************************************************
 ** @File Name:     music_main.xml
 ** @Module Name:   USBMusic
 ** @Description:   Applied new changes as per wideScreen implementation
                    By following document  AVN5.0_wide_guide_media_v0.1.pptx
                                           AVN5.0_wide_guide_common_v0.4.pptx
 ******************************************************************************
 ******************************************************************************
 **  @Revision History
 ******************************************************************************
 ** Revision#       Date(yyyy-mm-dd)             By                  Description
 ******************************************************************************
     1.0               2017-02-03              Samarth Dubey      main view xml file 
 ******************************************************************************
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="1280dip"
    android:layout_height="480dip" >

    <ImageView
        android:id="@+id/music_background"
        android:layout_width="1280dip"
        android:layout_height="420dip"
        android:layout_marginTop="60dip"
        android:background="@drawable/home_bg"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/music_main_bg"
        android:layout_width="1280dip"
        android:layout_height="420dip"
        android:layout_marginTop="60dip"/>
    
    <View
        android:id="@+id/music_main_bg_mask"
        android:layout_width="1280dip"
        android:layout_height="420dip"
        android:layout_marginTop="60dip"
        android:background="@drawable/me_bg_screen_bottom_on_mask"/>

    <LinearLayout
        android:layout_width="1280dip"
        android:layout_height="480dip"
        android:orientation="horizontal" 
	android:layoutDirection="ltr"
	>

        <include
            android:id="@+id/main_list_tab"
            android:layout_width="426dip"
            android:layout_height="480dip"
            layout="@layout/music_main_list_tab" />

        <FrameLayout
            android:layout_width="854dip"
            android:layout_height="480dip" >

            <include
                android:id="@+id/music_control"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                layout="@layout/music_main_control" />

            <include
                android:id="@+id/music_info"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                layout="@layout/music_main_play_info" />

            <include
                android:id="@+id/music_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                layout="@layout/music_main_progress" />

        </FrameLayout>

    </LinearLayout>
</FrameLayout>