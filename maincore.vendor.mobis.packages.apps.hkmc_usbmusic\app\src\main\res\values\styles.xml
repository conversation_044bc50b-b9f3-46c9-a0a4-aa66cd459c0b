<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="noAnimTheme" parent="android:Theme.NoTitleBar">
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowActionBar">true</item>
        <!--  Modified by thundersoft start : disable split touch && add transition animation   -->
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
        <!--  Modified by thundersoft end  -->
    </style>

    <style name="DialogTheme" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>
</resources>