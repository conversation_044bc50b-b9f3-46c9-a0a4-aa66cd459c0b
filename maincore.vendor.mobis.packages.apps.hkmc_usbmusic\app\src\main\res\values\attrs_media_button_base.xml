<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="MediaButtonBase">
        <attr name="textStrokeColor" format="integer" />
        <attr name="textStrokeWidth" format="dimension" />
        <attr name="extraImageCount" format="integer" />
        <attr name="onlyOneExtraImageDisplayStartIndex" format="integer" />
        <attr name="firstExtraImage" format="integer" />
        <attr name="secondExtraImage" format="integer" />
        <attr name="thirdExtraImage" format="integer" />
        <attr name="firstExtraImageVisible" format="boolean" />
        <attr name="firstExtraImageLeft" format="dimension" />
        <attr name="firstExtraImageTop" format="dimension" />
        <attr name="firstExtraImagePaddingLeft" format="dimension" />
        <attr name="firstExtraImagePaddingRight" format="dimension" />
        <attr name="firstExtraImagePaddingTop" format="dimension" />
        <attr name="firstExtraImagePaddingBottom" format="dimension" />
        <attr name="firstExtraImageWidth" format="dimension" />
        <attr name="firstExtraImageHeight" format="dimension" />
        <attr name="secondExtraImageVisible" format="boolean" />
        <attr name="secondExtraImageLeft" format="dimension" />
        <attr name="secondExtraImageTop" format="dimension" />
        <attr name="secondExtraImagePaddingLeft" format="dimension" />
        <attr name="secondExtraImagePaddingRight" format="dimension" />
        <attr name="secondExtraImagePaddingTop" format="dimension" />
        <attr name="secondExtraImagePaddingBottom" format="dimension" />
        <attr name="secondExtraImageWidth" format="dimension" />
        <attr name="secondExtraImageHeight" format="dimension" />
        <attr name="thirdExtraImageVisible" format="boolean" />
        <attr name="thirdExtraImageLeft" format="dimension" />
        <attr name="thirdExtraImageTop" format="dimension" />
        <attr name="thirdExtraImagePaddingLeft" format="dimension" />
        <attr name="thirdExtraImagePaddingRight" format="dimension" />
        <attr name="thirdExtraImagePaddingTop" format="dimension" />
        <attr name="thirdExtraImagePaddingBottom" format="dimension" />
        <attr name="thirdExtraImageWidth" format="dimension" />
        <attr name="thirdExtraImageHeight" format="dimension" />
        <attr name="extraImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>
        <attr name="firstExtraImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>
        <attr name="secondExtraImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>
        <attr name="thirdExtraImageGravity">
            <flag name="top" value="0x00000030" />
            <flag name="bottom" value="0x00000050" />
            <flag name="left" value="0x00000003" />
            <flag name="right" value="0x00000005" />
            <flag name="center_vertical" value="0x00000010" />
            <flag name="fill_vertical" value="0x00000070" />
            <flag name="center_horizontal" value="0x00000001" />
            <flag name="fill_horizontal" value="0x00000007" />
            <flag name="center" value="0x00000011" />
            <flag name="fill" value="0x00000077" />
        </attr>
    </declare-styleable>
</resources>
