<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/advanced_sound_effect"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="right">

    <LinearLayout
        android:id="@+id/advanced_sound_layout"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_marginTop="60dp"
        android:layout_gravity="right"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp"
        android:orientation="horizontal" >

        <LinearLayout
            android:id="@+id/meridian"
            android:layout_width="190dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:background="@drawable/music_main_button_bg"
            android:orientation="horizontal"
            android:visibility="gone"
            android:layoutDirection="rtl" >
            <LinearLayout
                android:id="@+id/meridianLED"
                android:layout_width="16dp"
                android:layout_height="52dp"
                android:layout_marginRight="12dp"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp"
                android:orientation="vertical" >
                <ImageView
                    android:id="@+id/meridian_Audience"
                    android:layout_width="match_parent"
                    android:layout_height="16dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/button_led_2" />

                <ImageView
                    android:id="@+id/meridian_Surround"
                    android:layout_width="match_parent"
                    android:layout_height="16dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/button_led_2" />

                <ImageView
                    android:id="@+id/meridian_Custom"
                    android:layout_width="match_parent"
                    android:layout_height="16dp"
                    android:src="@drawable/button_led_2" />
            </LinearLayout>

            <ImageView
                android:id="@+id/meridian_Sound"
                android:layout_width="138dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="12dp"
                android:layout_marginRight="12dp" />
        </LinearLayout>

        <LinearLayout
                android:id="@+id/arkamys"
                android:layout_width="172dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="4dp"
                android:layout_marginRight="4dp"
                android:background="@drawable/music_main_button_bg"
                android:orientation="horizontal"
                android:visibility="gone"
                android:layoutDirection="rtl" >

                <LinearLayout
                    android:id="@+id/arkamysLED"
                    android:layout_width="12dp"
                    android:layout_height="46dp"
                    android:layout_marginRight="14dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginBottom="7dp"
                    android:orientation="vertical" >

                    <ImageView
                        android:id="@+id/arkamys_Natural"
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginBottom="2dp"
                        android:src="@drawable/button_led_3" />

                    <ImageView
                        android:id="@+id/arkamys_Club"
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginBottom="2dp"
                        android:src="@drawable/button_led_3" />

                    <ImageView
                        android:id="@+id/arkamys_Lounge"
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginBottom="2dp"
                        android:src="@drawable/button_led_3" />

                    <ImageView
                        android:id="@+id/arkamys_Live"
                        android:layout_width="match_parent"
                        android:layout_height="10dp"
                        android:layout_marginBottom="2dp"
                        android:src="@drawable/button_led_3" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/Arkamys_Sound"
                    android:layout_width="122dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:src="@drawable/ic_media_arkamys_natural" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/qls"
                android:layout_width="198dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="4dp"
                android:layout_marginRight="4dp"
                android:background="@drawable/music_main_button_bg"
                android:orientation="horizontal"
                android:visibility="gone"
                android:layoutDirection="rtl" >

                <LinearLayout
                    android:id="@+id/QLS_LED"
                    android:layout_width="12dp"
                    android:layout_height="48dp"
                    android:layout_marginRight="14dp"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical" >

                    <ImageView
                        android:id="@+id/QLS_Audience"
                        android:layout_width="match_parent"
                        android:layout_height="22dp"
                        android:layout_marginBottom="4dp"
                        android:src="@drawable/button_led_2" />

                    <ImageView
                        android:id="@+id/QLS_OnStage"
                        android:layout_width="match_parent"
                        android:layout_height="22dp"
                        android:src="@drawable/button_led_2" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/QLS_Sound"
                    android:layout_width="148dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:src="@drawable/ic_media_qls_reference" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/LiveDynamic"
                android:layout_width="204dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="4dp"
                android:layout_marginRight="4dp"
                android:background="@drawable/music_main_button_bg"
                android:orientation="horizontal"
                android:visibility="gone"
                android:layoutDirection="rtl" >

                <LinearLayout
                    android:id="@+id/LiveDynamic_LED"
                    android:layout_width="12dp"
                    android:layout_height="48dp"
                    android:layout_marginRight="14dp"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical" >

                    <ImageView
                        android:id="@+id/LiveDynamic_Natural"
                        android:layout_width="match_parent"
                        android:layout_height="22dp"
                        android:layout_marginBottom="4dp"
                        android:src="@drawable/button_led_2" />

                    <ImageView
                        android:id="@+id/LiveDynamic_Concert"
                        android:layout_width="match_parent"
                        android:layout_height="22dp"
                        android:src="@drawable/button_led_2" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/LiveDynamic_Sound"
                    android:layout_width="148dp"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="12dp"
                    android:layout_marginRight="12dp"
                    android:src="@drawable/ic_media_livedynamic_reference" />

            </LinearLayout>

        <LinearLayout
                android:id="@+id/ArkamysButtonLayout"
                android:layout_width="174dp"
                android:layout_height="match_parent"
                android:layout_marginRight="4dp"
                android:background="@drawable/music_main_button_bg"
                android:orientation="horizontal"
                android:visibility="gone" >

                <ImageView
                    android:id="@+id/ArkamysButtonTextView"
                    android:layout_width="142dp"
                    android:layout_height="44dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="14dp"
                    android:layout_weight="0"
                    android:gravity="center_vertical"
                    android:src="@drawable/ic_media_arkamys" />

            </LinearLayout>

        <LinearLayout
            android:id="@+id/surroundButtonLayout"
            android:layout_width="174dp"
            android:layout_height="60dp"
            android:layout_marginLeft="4dp"
            android:background="@drawable/music_main_button_bg"
            android:orientation="horizontal"
            android:visibility="gone" >

            <ImageView
                android:id="@+id/surroundButtonOnOffImageView"
                android:layout_width="12dp"
                android:layout_height="60dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="14dp"
                android:layout_weight="0"
                android:gravity="center_vertical"
                android:src="@drawable/button_led_1" />

            <TextView
                android:id="@+id/surroundButtonTextView"
                android:layout_width="120dip"
                android:layout_height="60dip"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="14dp"
                android:layout_marginRight="14dp"
                android:gravity="center"
                android:lineSpacingExtra="-15sp"
                android:text="@string/surround"
                android:textColor="#ffffff"
                android:textSize="26sp" />
        </LinearLayout>
        
        <LinearLayout
            android:id="@+id/liveDynamicButtonPopupLayout"
            android:layout_width="218dip"
            android:layout_height="60dp"
            android:layout_marginLeft="4dp"
            android:background="@drawable/music_main_button_bg"
            android:orientation="horizontal"
            android:visibility="gone" >

            <TextView
                android:id="@+id/liveDynamicButtonPopupTextView"
                android:layout_width="190dip"
                android:layout_height="60dip"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="14dp"
                android:layout_marginRight="14dp"
                android:gravity="center"
                android:text="@string/live_dynamic"
                android:textColor="#ffffff"
                android:textSize="26sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/liveDynamicButtonLayout"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_marginLeft="4dp"
            android:gravity="center"
            android:background="@drawable/music_main_button_bg"
            android:orientation="horizontal"
            android:visibility="gone" > 

            <ImageView
                android:id="@+id/liveDynamicButtonOnOffImageView"
                android:layout_width="12dp"
                android:layout_height="60dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="14dp"
                android:layout_marginRight="14dp"
                android:layout_weight="0"
                android:gravity="right|center"
                android:src="@drawable/button_led_1" />

            <TextView
                    android:id="@+id/liveDynamicButtonTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="60dip"
                    android:layout_gravity="center"
                    android:layout_marginLeft="14dp"
                    android:gravity="right|center"
                    android:text="@string/live_dynamic"
                    android:textColor="#ffffff"
                    android:textSize="26sp" /> 
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ClariFiButtonLayout"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_marginLeft="4dp"
            android:background="@drawable/music_main_button_bg"
            android:orientation="horizontal"
            android:visibility="gone" >

            <ImageView
                android:id="@+id/ClariFiButtonOnOffImageView"
                android:layout_width="12dp"
                android:layout_height="60dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="14dp"
                android:layout_weight="0"
                android:gravity="center_vertical"
                android:src="@drawable/button_led_1" />

            <TextView
                android:id="@+id/ClariFiButtonTextView"
                android:layout_width="wrap_content"
                android:layout_height="60dip"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="14dp"
                android:layout_marginRight="14dp"
                android:gravity="center"
                android:text="@string/clari_fi"
                android:textColor="#ffffff"
                android:textSize="26sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/QLSButton"
            android:layout_width="92dp"
            android:layout_height="60dp"
            android:layout_marginLeft="4dp"
            android:gravity="center"
            android:background="@drawable/music_main_button_bg"
            android:orientation="horizontal"
            android:visibility="gone" >

            <ImageView
                android:id="@+id/QLSIcon"
                android:layout_width="50dp"
                android:layout_height="36dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_weight="0"
                android:src="@drawable/ic_media_qls" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/QLSLiteBttonLayout"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_marginLeft="4dp"
            android:layout_marginRight="4dp"
            android:background="@drawable/music_main_button_bg"
            android:orientation="horizontal"
            android:visibility="gone" >

            <ImageView
                android:id="@+id/qlsliteOnOffImageView"
                android:layout_width="12dp"
                android:layout_height="60dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="10dp"
                android:layout_marginLeft="14dp"
                android:layout_weight="0"
                android:gravity="center_vertical"
                android:src="@drawable/button_led_1" />

            <ImageView
                android:id="@+id/qlsliteOnButtonTextView"
                android:layout_width="50dp"
                android:layout_height="36dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="28dp"
                android:layout_weight="0"
                android:gravity="center_vertical"
                android:src="@drawable/ic_media_qls" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/centerpointButtonLayout"
            android:layout_width="wrap_content"
            android:layout_height="60dp"
            android:layout_marginLeft="4dp"
            android:background="@drawable/music_main_button_bg"
            android:orientation="horizontal"
            android:visibility="gone" >

            <ImageView
                android:id="@+id/centerpointOnOffImageView"
                android:layout_width="12dp"
                android:layout_height="60dp"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="14dp"
                android:layout_marginLeft="14dp"
                android:layout_weight="0"
                android:gravity="right|center"
                android:src="@drawable/button_led_1" />

            <ImageView
                android:id="@+id/centerpointButtonTextView"
                android:layout_width="144dp"
                android:layout_height="44dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="14dp"
                android:layout_weight="0"
                android:gravity="center_vertical"
                android:src="@drawable/ic_media_bose" />
        </LinearLayout>
    </LinearLayout>

</FrameLayout>
