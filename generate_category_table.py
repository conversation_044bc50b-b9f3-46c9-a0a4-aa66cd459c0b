#!/usr/bin/env python3
"""
Generate detailed category breakdown table for CWE0709.csv
"""

import pandas as pd

def generate_category_table():
    """Generate category breakdown table"""
    try:
        # Read CSV file
        df = pd.read_csv('CWE0709.csv')
        
        print("=" * 80)
        print("CWE-0709 CATEGORY BREAKDOWN TABLE")
        print("=" * 80)
        print(f"Total Issues: {len(df)}")
        print(f"Analysis Date: 2025-07-09")
        print()
        
        # Get category counts
        category_counts = df['Category'].value_counts()
        
        # Generate detailed table
        print("📊 DETAILED CATEGORY TABLE")
        print("-" * 80)
        print(f"{'Rank':<4} {'Category':<40} {'Count':<8} {'Percentage':<12} {'Priority'}")
        print("-" * 80)
        
        # Define priority levels based on security impact
        priority_map = {
            'High impact security': 'CRITICAL',
            'Medium impact security': 'HIGH',
            'Concurrent data access violations': 'HIGH',
            'Data race undermines locking': 'HIGH',
            'Program hangs': 'HIGH',
            'Null pointer dereferences': 'MEDIUM',
            'Resource leaks': 'MEDIUM',
            'Exceptional resource leaks': 'MEDIUM',
            'Low impact security': 'MEDIUM',
            'Control flow issues': 'LOW',
            'Integer handling issues': 'LOW',
            'Error handling issues': 'LOW',
            'Sigma': 'LOW',
            'Incorrect expression': 'LOW',
            'Class hierarchy inconsistencies': 'LOW',
            'Possible Control flow issues': 'LOW',
            'Code maintainability issues': 'LOW'
        }
        
        for i, (category, count) in enumerate(category_counts.items(), 1):
            percentage = (count / len(df)) * 100
            priority = priority_map.get(category, 'MEDIUM')
            
            # Truncate long category names for table formatting
            display_category = category[:38] + ".." if len(category) > 40 else category
            
            print(f"{i:<4} {display_category:<40} {count:<8} {percentage:<11.1f}% {priority}")
        
        print("-" * 80)
        print(f"{'TOTAL':<45} {len(df):<8} {'100.0%':<12}")
        print()
        
        # Security-focused breakdown
        print("🔒 SECURITY-FOCUSED CATEGORY ANALYSIS")
        print("-" * 60)
        
        security_categories = {
            'Critical Security': ['High impact security'],
            'High Security Risk': ['Medium impact security', 'Concurrent data access violations', 
                                 'Data race undermines locking', 'Program hangs'],
            'Medium Security Risk': ['Null pointer dereferences', 'Resource leaks', 
                                   'Exceptional resource leaks', 'Low impact security'],
            'Low Security Risk': ['Control flow issues', 'Integer handling issues', 
                                'Error handling issues', 'Sigma', 'Incorrect expression',
                                'Class hierarchy inconsistencies', 'Possible Control flow issues',
                                'Code maintainability issues']
        }
        
        for risk_level, categories in security_categories.items():
            total_count = 0
            for category in categories:
                if category in category_counts:
                    total_count += category_counts[category]
            
            percentage = (total_count / len(df)) * 100
            print(f"{risk_level:<25}: {total_count:>4} issues ({percentage:>5.1f}%)")
        
        print()
        
        # Top 5 categories detailed analysis
        print("🎯 TOP 5 CATEGORIES DETAILED ANALYSIS")
        print("-" * 60)
        
        top_5_categories = category_counts.head(5)
        for i, (category, count) in enumerate(top_5_categories.items(), 1):
            percentage = (count / len(df)) * 100
            category_issues = df[df['Category'] == category]
            
            # Status breakdown for this category
            status_counts = category_issues['Status'].value_counts()
            new_count = status_counts.get('New', 0)
            dismissed_count = status_counts.get('Dismissed', 0)
            
            print(f"{i}. {category}")
            print(f"   Total: {count} issues ({percentage:.1f}%)")
            print(f"   Status: New={new_count}, Dismissed={dismissed_count}")
            
            # Top issue types in this category
            if len(category_issues) > 0:
                top_types = category_issues['Type'].value_counts().head(3)
                print(f"   Top Types: {', '.join([f'{t}({c})' for t, c in top_types.items()])}")
            print()
        
        # Generate markdown table
        print("📋 MARKDOWN TABLE FORMAT")
        print("-" * 60)
        print("| Rank | Category | Count | Percentage | Priority |")
        print("|------|----------|-------|------------|----------|")
        
        for i, (category, count) in enumerate(category_counts.items(), 1):
            percentage = (count / len(df)) * 100
            priority = priority_map.get(category, 'MEDIUM')
            # Escape pipes in category names for markdown
            safe_category = category.replace('|', '\\|')
            print(f"| {i} | {safe_category} | {count} | {percentage:.1f}% | **{priority}** |")
        
        print()
        
        return category_counts
        
    except Exception as e:
        print(f"Error generating category table: {e}")
        return None

if __name__ == "__main__":
    category_data = generate_category_table()
