#!/usr/bin/env python3
"""
Script to analyze CWE0709.csv file - Coverity CWE (Common Weakness Enumeration) analysis
This is different from secure coding requirements and focuses on security vulnerabilities
"""

import pandas as pd
import sys
import os
from collections import Counter
from datetime import datetime

def analyze_cwe0709_csv(csv_file):
    """Analyze the CWE0709 CSV file"""
    try:
        # Read CSV file
        df = pd.read_csv(csv_file)
        
        print("=" * 80)
        print("CWE-0709 COVERITY ANALYSIS REPORT")
        print("=" * 80)
        print(f"File: {csv_file}")
        print(f"Total Issues: {len(df)}")
        print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Basic statistics
        print("📊 BASIC STATISTICS")
        print("-" * 40)
        print(f"Total CIDs: {len(df)}")
        print(f"Columns: {list(df.columns)}")
        print()
        
        # Status analysis
        print("📈 STATUS BREAKDOWN")
        print("-" * 40)
        status_counts = df['Status'].value_counts()
        for status, count in status_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{status}: {count} ({percentage:.1f}%)")
        print()
        
        # Impact analysis
        print("⚡ IMPACT LEVEL BREAKDOWN")
        print("-" * 40)
        impact_counts = df['Impact'].value_counts()
        for impact, count in impact_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{impact}: {count} ({percentage:.1f}%)")
        print()
        
        # Type analysis (Top 15)
        print("🔍 ISSUE TYPE BREAKDOWN (Top 15)")
        print("-" * 40)
        type_counts = df['Type'].value_counts().head(15)
        for issue_type, count in type_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{issue_type}: {count} ({percentage:.1f}%)")
        print()
        
        # Category analysis
        print("📂 CATEGORY BREAKDOWN")
        print("-" * 40)
        category_counts = df['Category'].value_counts()
        for category, count in category_counts.items():
            percentage = (count / len(df)) * 100
            print(f"{category}: {count} ({percentage:.1f}%)")
        print()
        
        # Security-focused analysis
        print("🔒 SECURITY IMPACT ANALYSIS")
        print("-" * 40)
        security_categories = ['High impact security', 'Low impact security', 'Sigma']
        security_issues = df[df['Category'].isin(security_categories)]
        print(f"Total security-related issues: {len(security_issues)}")
        
        if len(security_issues) > 0:
            print("\nSecurity issues by category:")
            security_cat_counts = security_issues['Category'].value_counts()
            for category, count in security_cat_counts.items():
                print(f"  {category}: {count}")
            
            print("\nSecurity issues by type:")
            security_type_counts = security_issues['Type'].value_counts()
            for issue_type, count in security_type_counts.items():
                print(f"  {issue_type}: {count}")
            
            print("\nSecurity issues status:")
            security_status = security_issues['Status'].value_counts()
            for status, count in security_status.items():
                print(f"  {status}: {count}")
        print()
        
        # High impact issues
        print("🚨 HIGH IMPACT ISSUES")
        print("-" * 40)
        high_impact = df[df['Impact'] == 'High']
        print(f"Total high impact issues: {len(high_impact)}")
        if len(high_impact) > 0:
            print("High impact issue types:")
            high_impact_types = high_impact['Type'].value_counts()
            for issue_type, count in high_impact_types.items():
                print(f"  {issue_type}: {count}")
            
            print("\nHigh impact status:")
            high_impact_status = high_impact['Status'].value_counts()
            for status, count in high_impact_status.items():
                print(f"  {status}: {count}")
        print()
        
        # File analysis
        print("📁 FILES WITH MOST ISSUES (Top 15)")
        print("-" * 40)
        file_counts = df['File'].value_counts().head(15)
        for file_path, count in file_counts.items():
            filename = file_path.split('/')[-1] if '/' in file_path else file_path
            print(f"{filename}: {count} issues")
        print()
        
        # Recent issues analysis
        print("📅 RECENT ISSUES ANALYSIS")
        print("-" * 40)
        recent_issues = df[df['First Detected'].str.contains('07/08/25', na=False)]
        print(f"Issues detected on 07/08/25: {len(recent_issues)}")
        if len(recent_issues) > 0:
            print("Recent issue types:")
            recent_types = recent_issues['Type'].value_counts()
            for issue_type, count in recent_types.items():
                print(f"  {issue_type}: {count}")
        print()
        
        # Thread-related issues
        print("🧵 THREAD-RELATED ISSUES")
        print("-" * 40)
        thread_keywords = ['Thread', 'deadlock', 'lock', 'Unguarded', 'Unlocked', 'race']
        thread_issues = df[df['Type'].str.contains('|'.join(thread_keywords), case=False, na=False)]
        print(f"Total thread-related issues: {len(thread_issues)}")
        if len(thread_issues) > 0:
            print("Thread issue types:")
            thread_types = thread_issues['Type'].value_counts()
            for issue_type, count in thread_types.items():
                print(f"  {issue_type}: {count}")
            
            print("\nThread issue status:")
            thread_status = thread_issues['Status'].value_counts()
            for status, count in thread_status.items():
                print(f"  {status}: {count}")
        print()
        
        # Null pointer issues
        print("🎯 NULL POINTER ISSUES")
        print("-" * 40)
        null_issues = df[df['Category'] == 'Null pointer dereferences']
        print(f"Total null pointer issues: {len(null_issues)}")
        if len(null_issues) > 0:
            print("Null pointer issue types:")
            null_types = null_issues['Type'].value_counts()
            for issue_type, count in null_types.items():
                print(f"  {issue_type}: {count}")
            
            print("\nNull pointer issue status:")
            null_status = null_issues['Status'].value_counts()
            for status, count in null_status.items():
                print(f"  {status}: {count}")
        print()
        
        # Framework services analysis
        print("🏗️ FRAMEWORK SERVICES ANALYSIS")
        print("-" * 40)
        framework_issues = df[df['File'].str.contains('framework/services', na=False)]
        print(f"Framework services issues: {len(framework_issues)}")
        if len(framework_issues) > 0:
            print("Framework services files:")
            framework_files = framework_issues['File'].value_counts()
            for file_path, count in framework_files.items():
                filename = file_path.split('/')[-1] if '/' in file_path else file_path
                print(f"  {filename}: {count}")
        print()
        
        # Progress analysis
        print("✅ PROGRESS ANALYSIS")
        print("-" * 40)
        new_count = len(df[df['Status'] == 'New'])
        dismissed_count = len(df[df['Status'] == 'Dismissed'])
        total_actionable = new_count + dismissed_count
        
        if total_actionable > 0:
            resolution_rate = (dismissed_count / total_actionable) * 100
            print(f"New issues: {new_count}")
            print(f"Dismissed issues: {dismissed_count}")
            print(f"Resolution rate: {resolution_rate:.1f}%")
        print()
        
        return df
        
    except Exception as e:
        print(f"Error analyzing CSV file: {e}")
        return None

def main():
    csv_file = "CWE0709.csv"
    
    if not os.path.exists(csv_file):
        print(f"CSV file {csv_file} not found")
        return
    
    df = analyze_cwe0709_csv(csv_file)
    
    if df is not None:
        print("=" * 80)
        print("ANALYSIS COMPLETE")
        print("=" * 80)
        print(f"Total CWE issues analyzed: {len(df)}")
        print("This analysis focuses on Common Weakness Enumeration (CWE)")
        print("which is different from secure coding standard violations.")
        print("CWE focuses on security vulnerabilities and weaknesses.")

if __name__ == "__main__":
    main()
