<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:music="http://schemas.android.com/apk/res/com.daudio.av.app.usbmusic"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/title_bar"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/title_bar_bg"
        android:layout_width="1280dip"
        android:layout_height="60dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="0dip"
        android:layout_marginTop="60dip"
        android:background="@drawable/co_bg_title_bar" />

    <View
        android:id="@+id/title_bar_trans_bg"
        android:layout_width="1280dip"
        android:layout_height="60dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="0dip"
        android:layout_marginTop="60dip"
        android:background="@drawable/co_bg_title_bar" />

    <TextView
        android:id="@+id/usb_title"
        android:layout_width="1280dip"
        android:layout_height="60dp"
        android:layout_marginTop="60dp"
        android:gravity="center"
        android:text="@string/music_title_usb"
        android:textColor="@color/text_music_title"
        android:textSize="28sp"
        android:textStyle="bold" />

    <com.daudio.av.app.usbmusic.ui.widget.button.MediaTextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="60dip"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="60dip"
        android:gravity="center"
        android:text="@string/music_title_usb"
        android:textColor="@color/text_music_title"
        android:textSize="28sp"
        android:visibility="gone"/>

    <FrameLayout
        android:id="@+id/list_subfolder_on"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_gravity="right|top"
        android:layout_marginLeft="8dip"
        android:layout_marginTop="60dip"        
        android:visibility="gone" >       

        <TextView
            android:id="@+id/subfolderTextView"
            android:layout_width="wrap_content"
            android:layout_height="60dip"
            android:paddingLeft="38dp"
            android:paddingRight="14dp"            
            android:background="@drawable/music_main_button_bg"
            android:gravity="center"
            android:text="@string/list_repeatall_in_folder"
            android:textColor="@color/text_music_title"
            android:textSize="26sp" />

        <ImageView
            android:id="@+id/subfolderOnOffImageView"
            android:layout_width="16dp"
            android:layout_height="32dp"
            android:layout_gravity="left|center"
            android:layout_marginLeft="12dp"
            android:src="@drawable/button_led_1" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/sound_effect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:visibility="gone" >
    </LinearLayout>

    <com.daudio.av.app.usbmusic.ui.widget.button.MediaButton
        android:id="@+id/title_list"
        android:layout_width="120dip"
        android:layout_height="60dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="8dip"
        android:layout_marginTop="60dip"
        android:background="@drawable/music_main_button_bg"
        android:gravity="center"
        android:onClick="onListBtn"
        android:text="@string/music_title_list"
        android:textColor="@drawable/music_main_button_text_color"
        android:textSize="26sp"
        music:extraImageGravity="center"
        music:firstExtraImage="@drawable/me_ic_list" />

    <ImageButton
        android:id="@+id/gotomain"
        android:layout_width="120dip"
        android:layout_height="60dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="8dip"
        android:layout_marginTop="60dip"
        android:background="@drawable/music_main_button_bg"
        android:gravity="center"
        android:src="@drawable/me_ic_bt_player_n"
        android:onClick="onGoToMainBtn"
        android:visibility="gone"/>

    <Button
        android:id="@+id/title_splitView"
        android:layout_width="426dip"
        android:layout_height="60dip"
        android:layout_gravity="left|top"
        android:layout_marginLeft="854dip"
        android:layout_marginTop="60dip"
        android:background="@drawable/split_bg_title"
        android:text="@string/split_view"
        android:textColor="@drawable/music_main_button_text_color"
        android:textSize="26sp" 
        android:visibility="gone"/>

</FrameLayout>
