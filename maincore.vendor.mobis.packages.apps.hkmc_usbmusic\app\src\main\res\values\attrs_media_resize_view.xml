<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="MediaResizeView">
        <attr name="resizable" format="boolean" />
        <attr name="secondViewText" format="integer" />
        <attr name="secondViewTextSize" format="dimension" />
        <attr name="secondViewTextColor" format="integer" />
        <attr name="secondViewTextStrokeColor" format="integer" />
        <attr name="secondViewTextStrokeWidth" format="dimension" />
        <attr name="secondViewLeftImage" format="integer" />
        <attr name="secondViewTopImage" format="integer" />
        <attr name="secondViewRightImage" format="integer" />
        <attr name="secondViewBottomImage" format="integer" />
        <attr name="secondViewBgImage" format="integer" />
        <attr name="secondViewLeft" format="dimension" />
        <attr name="secondViewTop" format="dimension" />
        <attr name="secondViewWidth" format="dimension" />
        <attr name="secondViewHeight" format="dimension" />
        <attr name="secondViewPaddingLeft" format="dimension" />
        <attr name="secondViewPaddingTop" format="dimension" />
        <attr name="secondViewPaddingRight" format="dimension" />
        <attr name="secondViewPaddingBottom" format="dimension" />
        <attr name="thirdViewText" format="integer" />
        <attr name="thirdViewTextSize" format="dimension" />
        <attr name="thirdViewTextColor" format="integer" />
        <attr name="thirdViewTextStrokeColor" format="integer" />
        <attr name="thirdViewTextStrokeWidth" format="dimension" />
        <attr name="thirdViewLeftImage" format="integer" />
        <attr name="thirdViewTopImage" format="integer" />
        <attr name="thirdViewRightImage" format="integer" />
        <attr name="thirdViewBottomImage" format="integer" />
        <attr name="thirdViewBgImage" format="integer" />
        <attr name="thirdViewLeft" format="dimension" />
        <attr name="thirdViewTop" format="dimension" />
        <attr name="thirdViewWidth" format="dimension" />
        <attr name="thirdViewHeight" format="dimension" />
        <attr name="thirdViewPaddingLeft" format="dimension" />
        <attr name="thirdViewPaddingTop" format="dimension" />
        <attr name="thirdViewPaddingRight" format="dimension" />
        <attr name="thirdViewPaddingBottom" format="dimension" />
    </declare-styleable>
</resources>